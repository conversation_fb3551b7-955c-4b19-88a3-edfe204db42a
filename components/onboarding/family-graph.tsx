'use client';

import { useEffect, useRef, useState } from 'react';
import mermaid from 'mermaid';
import { Users, User, Baby } from 'lucide-react';
import { motion } from 'framer-motion';

interface FamilyGraphProps {
  parentInfo: {
    name: string;
    email: string;
  };
  children?: Array<{
    name: string;
    age: number;
  }>;
  familyMembers?: Array<{
    name: string;
    relationship: string;
    age: number;
    dateOfBirth?: string;
    gender?: string;
  }>;
}

export function FamilyGraph({ parentInfo, children, familyMembers }: FamilyGraphProps) {
  const graphRef = useRef<HTMLDivElement>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Initialize mermaid with clean professional style
    mermaid.initialize({
      startOnLoad: true,
      theme: 'neutral',
      themeVariables: {
        primaryColor: '#06b6d4',
        primaryTextColor: '#1e293b',
        primaryBorderColor: '#0891b2',
        lineColor: '#64748b',
        secondaryColor: '#3b82f6',
        tertiaryColor: '#8b5cf6',
        background: '#f8fafc',
        mainBkg: '#06b6d4',
        secondBkg: '#3b82f6',
        tertiaryBkg: '#8b5cf6',
        nodeBorder: '#334155',
        fontFamily: 'Inter, system-ui, sans-serif',
        fontSize: '14px',
      },
      flowchart: {
        curve: 'basis',
        padding: 15,
        nodeSpacing: 40,
        rankSpacing: 60,
        diagramPadding: 20,
      },
    });
    setIsInitialized(true);
  }, []);

  useEffect(() => {
    if (!isInitialized || !graphRef.current) return;

    const renderGraph = async () => {
      // Clear previous graph
      graphRef.current!.innerHTML = '';

      // Combine all family members from both children and familyMembers
      const allMembers = [
        ...(children || []).map(child => ({
          name: child.name,
          relationship: 'child',
          age: child.age,
        })),
        ...(familyMembers || [])
      ];

      // Build the mermaid graph definition with clean rounded rectangles matching reference style
      let graphDefinition = 'flowchart TD\n';

      // Add parent node
      const parentName = parentInfo.name || 'Parent';
      graphDefinition += `    Parent("${parentName}")\n`;

      // Add family member nodes and connections
      if (allMembers.length > 0) {
        allMembers.forEach((member, index) => {
          const memberId = `Member${index + 1}`;
          const memberName = member.name || `Member ${index + 1}`;
          const ageDisplay = member.age > 0 ? ` (${member.age})` : '';
          const memberLabel = `${memberName}${ageDisplay}`;
          
          graphDefinition += `    ${memberId}("${memberLabel}")\n`;
          graphDefinition += `    Parent --> ${memberId}\n`;
          
          // Determine relationship type for styling
          const relationship = member.relationship?.toLowerCase() || '';
          if (relationship.includes('child') || relationship.includes('son') || relationship.includes('daughter')) {
            graphDefinition += `    ${memberId}:::childClass\n`;
          } else if (relationship.includes('spouse') || relationship.includes('partner') || relationship.includes('wife') || relationship.includes('husband')) {
            graphDefinition += `    ${memberId}:::spouseClass\n`;
          } else {
            graphDefinition += `    ${memberId}:::otherClass\n`;
          }
        });
      }

      // Add clean minimal styling exactly matching your reference image
      graphDefinition += `\n    classDef default fill:#f8f9ff,stroke:#6366f1,stroke-width:2px,color:#1e293b,font-size:14px\n`;
      graphDefinition += `    classDef parentClass fill:#ddd6fe,stroke:#7c3aed,stroke-width:2px,color:#1e293b,font-weight:600\n`;
      graphDefinition += `    classDef childClass fill:#e0e7ff,stroke:#6366f1,stroke-width:2px,color:#1e293b\n`;
      graphDefinition += `    classDef spouseClass fill:#fef3c7,stroke:#f59e0b,stroke-width:2px,color:#1e293b\n`;
      graphDefinition += `    classDef otherClass fill:#dcfce7,stroke:#16a34a,stroke-width:2px,color:#1e293b\n`;
      graphDefinition += `    class Parent parentClass\n`;

      try {
        const { svg } = await mermaid.render('familyGraph', graphDefinition);
        if (graphRef.current) {
          graphRef.current.innerHTML = svg;
        }
      } catch (error) {
        console.error('Error rendering mermaid graph:', error);
        // Fallback to simple visualization
        if (graphRef.current) {
          graphRef.current.innerHTML = `
            <div class="flex flex-col items-center justify-center h-full text-gray-500">
              <Users class="h-12 w-12 mb-2" />
              <p class="text-sm">Family visualization will appear here</p>
            </div>
          `;
        }
      }
    };

    renderGraph();
  }, [isInitialized, parentInfo, children, familyMembers]);

  // Fallback simple visualization if mermaid fails
  const renderSimpleGraph = () => {
    return (
      <div className="flex flex-col items-center justify-center h-full p-8">
        {/* Parent Node */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="bg-teal-500 text-white rounded-lg px-6 py-3 shadow-lg flex items-center gap-2"
        >
          <User className="h-5 w-5" />
          <span className="font-semibold">{parentInfo.name || 'Parent'}</span>
        </motion.div>

        {/* Connections */}
        {children && children.length > 0 && (
          <div className="flex flex-col items-center">
            <div className="w-0.5 h-8 bg-teal-300 my-2" />
            <div className="flex gap-4">
              {children.map((child, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div className="w-0.5 h-4 bg-purple-300" />
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-purple-500 text-white rounded-lg px-4 py-2 shadow-md flex items-center gap-2"
                  >
                    <Baby className="h-4 w-4" />
                    <div className="text-sm">
                      <div className="font-medium">{child.name || `Child ${index + 1}`}</div>
                      <div className="text-xs opacity-90">Age: {child.age || 'N/A'}</div>
                    </div>
                  </motion.div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Empty State */}
        {children && children.length === 0 && (
          <div className="mt-8 text-center text-gray-500">
            <p className="text-sm">Add family members to see the graph</p>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="h-full w-full bg-white rounded-lg p-4 overflow-auto">
      <div ref={graphRef} className="h-full w-full flex items-center justify-center">
        {/* Mermaid graph will be rendered here */}
        {!isInitialized && renderSimpleGraph()}
      </div>
    </div>
  );
}