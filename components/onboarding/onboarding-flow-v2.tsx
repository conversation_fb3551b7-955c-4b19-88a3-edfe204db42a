'use client';

import { useState, useCallback } from 'react';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { X, CheckCircle, ArrowLeft, ArrowRight, User, UserPlus, AlertCircle } from 'lucide-react';
import { FamilyGraph } from './family-graph';
import { MaeInteractiveUI } from './mae-interactive-ui';
import { ThemeToggle } from '@/components/theme-toggle';
import { cn } from '@/lib/utils';

// Define step types for better type safety
type OnboardingStep = 'user_info' | 'family_info' | 'complete';

interface OnboardingData {
  familyMembers: Array<{
    name: string;
    relationship: string;
    age: number;
    dateOfBirth?: string;
    gender?: string;
    medicalConditions?: string;
    allergies?: string;
    medications?: string;
    notes?: string;
  }>;
  parentInfo: {
    name: string;
    email: string;
    phone?: string;
    location?: string;
    role?: string;
  };
  children: Array<{
    name: string;
    age: number;
    grade?: string;
    interests?: string[];
  }>;
  preferences?: {
    communicationStyle?: string;
    primaryConcerns?: string[];
  };
}

// Step configuration for better maintainability
const ONBOARDING_STEPS: Array<{
  key: OnboardingStep;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
}> = [
  {
    key: 'user_info',
    title: 'Your Information',
    description: 'Tell us about yourself to get started',
    icon: User,
  },
  {
    key: 'family_info',
    title: 'Family Members',
    description: 'Add your family members for personalized guidance',
    icon: UserPlus,
  },
  {
    key: 'complete',
    title: 'Complete',
    description: 'You\'re all set! Welcome to Our Kidz',
    icon: CheckCircle,
  },
];

export function OnboardingFlowV2() {
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('user_info');
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    parentInfo: {
      name: '',
      email: '',
      phone: '',
      location: '',
      role: 'parent',
    },
    children: [],
    familyMembers: [],
  });

  // Calculate progress based on current step
  const currentStepIndex = ONBOARDING_STEPS.findIndex(step => step.key === currentStep);
  const progress = ((currentStepIndex + 1) / ONBOARDING_STEPS.length) * 100;

  // Step validation helpers
  const getValidationErrors = useCallback(() => {
    const errors: string[] = [];

    switch (currentStep) {
      case 'user_info':
        if (!onboardingData.parentInfo.name.trim()) {
          errors.push('Name is required');
        }
        if (!onboardingData.parentInfo.email.trim()) {
          errors.push('Email is required');
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(onboardingData.parentInfo.email)) {
          errors.push('Please enter a valid email address');
        }
        break;
      case 'family_info':
        // Family members are optional, no validation errors
        break;
      case 'complete':
        // No validation needed for completion step
        break;
    }

    return errors;
  }, [currentStep, onboardingData]);

  // Step navigation helpers
  const canGoNext = useCallback(() => {
    const errors = getValidationErrors();
    return errors.length === 0 && currentStep !== 'complete';
  }, [currentStep, getValidationErrors]);

  const canGoPrevious = useCallback(() => {
    return currentStep !== 'user_info';
  }, [currentStep]);

  const goToNextStep = useCallback(() => {
    const currentIndex = ONBOARDING_STEPS.findIndex(step => step.key === currentStep);
    if (currentIndex < ONBOARDING_STEPS.length - 1) {
      const nextStep = ONBOARDING_STEPS[currentIndex + 1];
      setCurrentStep(nextStep.key);
    }
  }, [currentStep]);

  const goToPreviousStep = useCallback(() => {
    const currentIndex = ONBOARDING_STEPS.findIndex(step => step.key === currentStep);
    if (currentIndex > 0) {
      const previousStep = ONBOARDING_STEPS[currentIndex - 1];
      setCurrentStep(previousStep.key);
    }
  }, [currentStep]);



  const handleClose = () => {
    // Handle closing the onboarding flow
    window.location.href = '/';
  };

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-teal-50 via-white to-teal-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 z-50 overflow-hidden">
      {/* Main Container */}
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
          <div className="flex items-center justify-between max-w-7xl mx-auto">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <img
                  src="/OKdarkTsp.png"
                  alt="Our Kidz Logo"
                  className="h-8 w-8 object-contain"
                />
              </div>
              <h1 className="text-xl text-gray-900 dark:text-white">
                Welcome to Our Kidz
              </h1>
            </div>
            <div className="flex items-center gap-2">
              <ThemeToggle />
              <button
                onClick={handleClose}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                aria-label="Close onboarding"
              >
                <X className="h-5 w-5 text-gray-500" />
              </button>
            </div>
          </div>
        </div>

        {/* Progress Bar and Step Info */}
        <div className="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-100 dark:border-gray-700">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-between mb-3">
              <div>
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {ONBOARDING_STEPS[currentStepIndex]?.title}
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {ONBOARDING_STEPS[currentStepIndex]?.description}
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Step {currentStepIndex + 1} of {ONBOARDING_STEPS.length}
                </p>
              </div>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full max-w-7xl mx-auto px-6 py-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
              {/* Left Panel - Mae's Interactive UI */}
              <Card className="bg-gradient-to-br from-teal-50 to-cyan-50 dark:from-gray-800 dark:to-gray-900 border-teal-200 dark:border-gray-600 overflow-hidden">
                <CardHeader className="pb-4">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                    Mae's Interactive UI
                  </h2>
                </CardHeader>
                <CardContent className="h-[calc(100%-5rem)] overflow-hidden">
                  <MaeInteractiveUI
                    currentStep={currentStep}
                    onboardingData={onboardingData}
                    onDataUpdate={setOnboardingData}
                    onStepChange={setCurrentStep}
                  />
                </CardContent>
              </Card>

              {/* Right Panel - Family Graph */}
              <Card className="bg-gradient-to-br from-teal-50 to-teal-100 dark:from-gray-800 dark:to-gray-900 border-teal-200 dark:border-gray-600 overflow-hidden">
                <CardHeader className="pb-4">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                    Family Graph
                  </h2>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    As family members are added, the mermaid graph updates in real-time
                  </p>
                </CardHeader>
                <CardContent className="h-[calc(100%-6rem)] overflow-hidden">
                  <FamilyGraph 
                    parentInfo={onboardingData.parentInfo}
                    children={onboardingData.children}
                    familyMembers={onboardingData.familyMembers}
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Navigation Footer */}
        <div className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 px-6 py-4">
          <div className="max-w-7xl mx-auto flex justify-between items-center">
            <Button
              variant="outline"
              onClick={goToPreviousStep}
              disabled={!canGoPrevious()}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Previous
            </Button>

            <div className="flex items-center gap-2">
              {ONBOARDING_STEPS.map((step, index) => {
                const StepIcon = step.icon;
                const isActive = step.key === currentStep;
                const isCompleted = currentStepIndex > index;

                return (
                  <div key={step.key} className="flex items-center">
                    <div className={cn(
                      "flex items-center justify-center w-8 h-8 rounded-full border-2 transition-colors",
                      isActive && "border-teal-500 bg-teal-500 text-white",
                      isCompleted && "border-green-500 bg-green-500 text-white",
                      !isActive && !isCompleted && "border-gray-300 text-gray-400"
                    )}>
                      <StepIcon className="h-4 w-4" />
                    </div>
                    {index < ONBOARDING_STEPS.length - 1 && (
                      <div className={cn(
                        "w-8 h-0.5 mx-2",
                        isCompleted ? "bg-green-500" : "bg-gray-300"
                      )} />
                    )}
                  </div>
                );
              })}
            </div>

            <div className="flex flex-col items-end gap-2">
              {/* Validation errors */}
              {getValidationErrors().length > 0 && currentStep !== 'complete' && (
                <div className="text-sm text-red-600 dark:text-red-400 text-right">
                  {getValidationErrors().map((error, index) => (
                    <div key={index} className="flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {error}
                    </div>
                  ))}
                </div>
              )}

              <Button
                onClick={() => {
                  if (currentStep === 'complete') {
                    console.log('Onboarding complete:', onboardingData);
                    // Handle completion - could redirect to dashboard
                    window.location.href = '/dashboard';
                  } else {
                    goToNextStep();
                  }
                }}
                disabled={!canGoNext() && currentStep !== 'complete'}
                className="bg-teal-600 hover:bg-teal-700 flex items-center gap-2"
              >
                {currentStep === 'complete' ? 'Get Started' : 'Next'}
                {currentStep !== 'complete' && <ArrowRight className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}