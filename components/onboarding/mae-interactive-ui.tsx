'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Plus, Trash2, Sparkles, User, Mail, Phone, MapPin, Calendar, Heart } from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  aguiEventListener, 
  AG_UI_EVENTS, 
  type FillUserFormPayload,
  type AddFamilyMemberPayload,
  type EventResponse
} from '@/lib/ag-ui-events';
import { useCopilotAction } from '@copilotkit/react-core';

interface MaeInteractiveUIProps {
  streamedInfo: string[];
  currentStep: number;
  onboardingData: any;
  onDataUpdate: (data: any) => void;
}

export function MaeInteractiveUI({ 
  streamedInfo, 
  currentStep, 
  onboardingData, 
  onDataUpdate 
}: MaeInteractiveUIProps) {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [localData, setLocalData] = useState(onboardingData);

  useEffect(() => {
    setLocalData(onboardingData);
  }, [onboardingData]);

  useEffect(() => {
    // Auto-scroll to bottom when new info is added
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [streamedInfo]);

  // Mae's CopilotKit Actions - This is the proper AG-UI integration!
  useCopilotAction({
    name: "fillUserField",
    description: "Fill a specific field in the user profile form during onboarding. Mae uses this to update user information in real-time as the user speaks.",
    parameters: [
      {
        name: "field",
        type: "string", 
        description: "The field to update (name, email, phone, location, role, etc.)",
        required: true,
      },
      {
        name: "value",
        type: "string",
        description: "The value to set for the field",
        required: true,
      }
    ],
    handler: async ({ field, value }: { field: string; value: string }) => {
      console.log(`🔥 Mae CopilotKit Action: Filling field ${field} with value:`, value);
      
      // Map fields to match our form structure
      const fieldMappings: Record<string, string> = {
        'name': 'name',
        'full_name': 'name',
        'display_name': 'name', 
        'first_name': 'name',
        'last_name': 'name',
        'email': 'email',
        'phone': 'phone',
        'phone_number': 'phone',
        'location': 'location',
        'zip': 'location',
        'zip_code': 'location',
        'role': 'role'
      };

      const targetField = fieldMappings[field.toLowerCase()] || field;
      
      if (targetField in localData.parentInfo) {
        handleParentInfoChange(targetField, value);
        return `Successfully updated ${targetField} to: ${value}`;
      }

      return `Field ${field} not found in user profile form`;
    },
  });

  useCopilotAction({
    name: "addFamilyMember", 
    description: "Add a new family member (child, spouse, etc.) to the user profile. Mae uses this when user mentions family members during conversation.",
    parameters: [
      {
        name: "name",
        type: "string",
        description: "The family member's name",
        required: true,
      },
      {
        name: "relationship",
        type: "string", 
        description: "Relationship to user (child, spouse, daughter, son, etc.)",
        required: true,
      },
      {
        name: "dateOfBirth", 
        type: "string",
        description: "Date of birth in YYYY-MM-DD format (optional)",
        required: false,
      },
      {
        name: "age",
        type: "number",
        description: "Age in years (optional)",
        required: false,
      },
      {
        name: "gender",
        type: "string", 
        description: "Gender (male, female, non-binary, etc.)",
        required: false,
      }
    ],
    handler: async ({ name, relationship, dateOfBirth, age, gender }: { 
      name: string; 
      relationship: string; 
      dateOfBirth?: string; 
      age?: number; 
      gender?: string; 
    }) => {
      console.log(`🔥 Mae CopilotKit Action: Adding family member`, { name, relationship, dateOfBirth, age, gender });
      
      // Calculate date of birth from age if needed
      let finalDateOfBirth = dateOfBirth;
      if (!finalDateOfBirth && age) {
        const currentYear = new Date().getFullYear();
        const birthYear = currentYear - age;
        finalDateOfBirth = `${birthYear}-01-01`;
      }

      const familyMember = {
        name,
        relationship,
        dateOfBirth: finalDateOfBirth || '',
        age: age || 0,
        gender: gender || '',
        medicalConditions: '',
        allergies: '',
        medications: '',
        notes: ''
      };

      // Add to local state
      const newFamilyMembers = [...(localData.familyMembers || []), familyMember];
      setLocalData((prev: any) => ({ ...prev, familyMembers: newFamilyMembers }));
      onDataUpdate({ ...localData, familyMembers: newFamilyMembers });

      return `Successfully added family member: ${name} (${relationship})${age ? `, age ${age}` : ''}`;
    },
  });

  useCopilotAction({
    name: "moveToNextStep",
    description: "Move to the next step in the onboarding process. Mae uses this to guide the user through the onboarding flow.",
    parameters: [
      {
        name: "step",
        type: "string",
        description: "The step to move to (user_info, family_info, complete)",
        required: true,
      }
    ],
    handler: async ({ step }: { step: string }) => {
      console.log(`🔥 Mae CopilotKit Action: Moving to step:`, step);
      
      if (step === 'family_info') {
        // Move to family member section - set currentStep to 1
        const updatedData = { ...localData, currentStep: 1 };
        setLocalData(updatedData);
        onDataUpdate(updatedData);
        return `Successfully moved to family member section`;
      }

      return `Moved to step: ${step}`;
    },
  });

  useCopilotAction({
    name: "buildFamilyGraph",
    description: "Build and display the family graph in real-time. Mae uses this when users ask to see the family graph or when building it dynamically during conversation.",
    parameters: [
      {
        name: "parentName",
        type: "string",
        description: "The parent/user's name",
        required: true,
      },
      {
        name: "familyMembers",
        type: "object",
        description: "Array of family members with their details",
        required: false,
      }
    ],
    handler: async ({ parentName, familyMembers: membersList }: { 
      parentName: string; 
      familyMembers?: Array<{
        name: string;
        relationship: string;
        age?: number;
        dateOfBirth?: string;
        gender?: string;
      }>; 
    }) => {
      console.log(`🔥 Mae CopilotKit Action: Building family graph for ${parentName}`, membersList);
      
      // Update parent info
      const updatedParentInfo = { ...localData.parentInfo, name: parentName };
      
      // Process family members
      const processedMembers = (membersList || []).map(member => ({
        name: member.name,
        relationship: member.relationship,
        age: member.age || 0,
        dateOfBirth: member.dateOfBirth || '',
        gender: member.gender || '',
        medicalConditions: '',
        allergies: '',
        medications: '',
        notes: ''
      }));

      // Update local state with new family data
      const updatedData = {
        ...localData,
        parentInfo: updatedParentInfo,
        familyMembers: processedMembers
      };

      setLocalData(updatedData);
      onDataUpdate(updatedData);

      // Generate a summary of the family graph
      const memberSummary = processedMembers.length > 0 
        ? processedMembers.map(m => `${m.name} (${m.relationship}${m.age ? `, age ${m.age}` : ''})`).join(', ')
        : 'no family members yet';

      return `Successfully built family graph for ${parentName} with ${memberSummary}. The graph is now displayed in real-time on the right side of the screen.`;
    },
  });

  useCopilotAction({
    name: "showFamilyGraph", 
    description: "Display the current family graph. Mae uses this when users specifically ask to see or view the family graph.",
    parameters: [],
    handler: async () => {
      console.log(`🔥 Mae CopilotKit Action: Showing current family graph`);
      
      const parentName = localData.parentInfo.name || 'User';
      const familyCount = (localData.familyMembers || []).length;
      const childrenCount = (localData.children || []).length;
      const totalMembers = familyCount + childrenCount;

      if (totalMembers === 0) {
        return `The family graph shows ${parentName} with no family members added yet. You can add family members by telling me about them, like "I have a 5-year-old daughter named Emma."`;
      }

      const membersList = [
        ...(localData.familyMembers || []).map((m: { name: any; relationship: any; age: any; }) => `${m.name} (${m.relationship}${m.age ? `, age ${m.age}` : ''})`),
        ...(localData.children || []).map((c: { name: any; age: any; }) => `${c.name} (child, age ${c.age})`)
      ];

      return `The family graph shows ${parentName} connected to ${totalMembers} family member${totalMembers > 1 ? 's' : ''}: ${membersList.join(', ')}. You can see the visual graph on the right side of the screen with color-coded relationships.`;
    },
  });

  // AG-UI Event Handlers for Mae integration
  const handleFillUserForm = async (payload: FillUserFormPayload): Promise<EventResponse> => {
    console.log('🔥 V2: Mae filling user form:', payload);
    
    const fieldMappings: Record<string, string> = {
      'name': 'name',
      'email': 'email', 
      'phone': 'phone',
      'zip': 'location'
    };

    const targetField = fieldMappings[payload.field] || payload.field;
    
    if (targetField in localData.parentInfo) {
      handleParentInfoChange(targetField, payload.value as string);
      return { success: true, message: `Updated ${targetField}` };
    }

    return { success: false, error: 'Unknown field' };
  };

  const handleAddFamilyMember = async (payload: AddFamilyMemberPayload): Promise<EventResponse> => {
    console.log('🔥 V2: Mae adding family member:', payload);
    
    const birthYear = new Date().getFullYear() - Math.floor((Date.now() - new Date(payload.date_of_birth).getTime()) / (365.25 * 24 * 60 * 60 * 1000));
    const age = new Date().getFullYear() - birthYear;
    
    const newChild = {
      name: payload.name,
      age: age,
      grade: payload.relationship === 'child' ? `Grade ${Math.max(1, age - 5)}` : undefined,
      interests: []
    };

    const newData = {
      ...localData,
      children: [...localData.children, newChild]
    };

    setLocalData(newData);
    onDataUpdate(newData);
    
    return { success: true, message: `Added ${payload.name}` };
  };

  // Register AG-UI event listeners + Direct UI Bridge + Force UI System
  useEffect(() => {
    console.log('🔥 V2: Registering Mae AG-UI listeners with triple redundancy');
    
    // AG-UI Event System (Primary)
    aguiEventListener.addEventListener(AG_UI_EVENTS.FILL_USER_FORM, handleFillUserForm);
    aguiEventListener.addEventListener(AG_UI_EVENTS.ADD_FAMILY_MEMBER, handleAddFamilyMember);
    
    // Direct UI Bridge (Secondary - Backup)
    // const setupDirectUIBridge = async () => {
    //   const { maeUIBridge } = await import('@/lib/mae-direct-ui-bridge');
    //   console.log('🔥 V2: Connected to Direct UI Bridge') ;
    //   maeUIBridge.subscribe('fillUserField', (field: string, value: any) => {
    //     console.log('🔥 V2: Direct UI Bridge update:', { field, value });
    //     handleParentInfoChange(field, value);
    //   });
      
    //   maeUIBridge.subscribe('addFamilyMember', (member: any) => {
    //     console.log('🔥 V2: Direct UI Bridge add family member:', member);
    //     const birthYear = new Date().getFullYear() - Math.floor((Date.now() - new Date(member.date_of_birth).getTime()) / (365.25 * 24 * 60 * 60 * 1000));
    //     const age = new Date().getFullYear() - birthYear;
        
    //     const newChild = {
    //       name: member.name,
    //       age: age,
    //       grade: member.relationship === 'child' ? `Grade ${Math.max(1, age - 5)}` : undefined,
    //       interests: []
    //     };
        
    //     const newData = {
    //       ...localData,
    //       children: [...localData.children, newChild]
    //     };
        
    //     setLocalData(newData);
    //     onDataUpdate(newData);
    //   }
    
    // setupDirectUIBridge();
    
    // Force UI System (Tertiary - Ultimate Backup)
    const handleForceUIUpdate = (event: any) => {
      console.log('🔥 V2: Force UI System update:', event.detail);
      const { action, data } = event.detail;
      
      if (action === 'fill_field') {
        handleParentInfoChange(data.field, data.value);
      } else if (action === 'add_family_member') {
        const member = data;
        const birthYear = new Date().getFullYear() - Math.floor((Date.now() - new Date(member.date_of_birth).getTime()) / (365.25 * 24 * 60 * 60 * 1000));
        const age = new Date().getFullYear() - birthYear;
        
        const newChild = {
          name: member.name,
          age: age,
          grade: member.relationship === 'child' ? `Grade ${Math.max(1, age - 5)}` : undefined,
          interests: []
        };
        
        const newData = {
          ...localData,
          children: [...localData.children, newChild]
        };
        
        setLocalData(newData);
        onDataUpdate(newData);
      }
    };
    
    window.addEventListener('FORCE_MAE_UI_UPDATE', handleForceUIUpdate);
    
    return () => {
      console.log('🔥 V2: Cleaning up Mae AG-UI listeners');
      aguiEventListener.removeEventListener(AG_UI_EVENTS.FILL_USER_FORM);
      aguiEventListener.removeEventListener(AG_UI_EVENTS.ADD_FAMILY_MEMBER);
      window.removeEventListener('FORCE_MAE_UI_UPDATE', handleForceUIUpdate);
    };
  }, [localData, onDataUpdate]);

  const handleParentInfoChange = (field: string, value: string) => {
    const newData = {
      ...localData,
      parentInfo: {
        ...localData.parentInfo,
        [field]: value
      }
    };
    setLocalData(newData);
    onDataUpdate(newData);
  };

  const handleAddChild = () => {
    const newData = {
      ...localData,
      children: [
        ...localData.children,
        { name: '', age: 0, interests: [] }
      ]
    };
    setLocalData(newData);
    onDataUpdate(newData);
  };

  const handleChildChange = (index: number, field: string, value: any) => {
    const newChildren = [...localData.children];
    newChildren[index] = {
      ...newChildren[index],
      [field]: value
    };
    const newData = {
      ...localData,
      children: newChildren
    };
    setLocalData(newData);
    onDataUpdate(newData);
  };

  const handleRemoveChild = (index: number) => {
    const newChildren = localData.children.filter((_: any, i: number) => i !== index);
    const newData = {
      ...localData,
      children: newChildren
    };
    setLocalData(newData);
    onDataUpdate(newData);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // Parent Information
        return (
          <div className="space-y-6">
            <div className="space-y-3">
              <Label htmlFor="parent-name" className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                <User className="h-4 w-4 text-teal-600 dark:text-teal-400" />
                Your Name
              </Label>
              <Input
                id="parent-name"
                placeholder="Enter your name"
                value={localData.parentInfo.name}
                onChange={(e) => handleParentInfoChange('name', e.target.value)}
                className="border-teal-200 focus:border-teal-400 dark:border-teal-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400"
              />
            </div>

            <div className="space-y-3">
              <Label htmlFor="parent-email" className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                <Mail className="h-4 w-4 text-teal-600 dark:text-teal-400" />
                Email Address
              </Label>
              <Input
                id="parent-email"
                type="email"
                placeholder="<EMAIL>"
                value={localData.parentInfo.email}
                onChange={(e) => handleParentInfoChange('email', e.target.value)}
                className="border-teal-200 focus:border-teal-400 dark:border-teal-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400"
              />
            </div>

            <div className="space-y-3">
              <Label htmlFor="parent-phone" className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                <Phone className="h-4 w-4 text-teal-600 dark:text-teal-400" />
                Phone Number (Optional)
              </Label>
              <Input
                id="parent-phone"
                type="tel"
                placeholder="(*************"
                value={localData.parentInfo.phone || ''}
                onChange={(e) => handleParentInfoChange('phone', e.target.value)}
                className="border-teal-200 focus:border-teal-400 dark:border-teal-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400"
              />
            </div>

            <div className="space-y-3">
              <Label htmlFor="parent-location" className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                <MapPin className="h-4 w-4 text-teal-600 dark:text-teal-400" />
                Location (Optional)
              </Label>
              <Input
                id="parent-location"
                placeholder="City, State"
                value={localData.parentInfo.location || ''}
                onChange={(e) => handleParentInfoChange('location', e.target.value)}
                className="border-teal-200 focus:border-teal-400 dark:border-teal-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400"
              />
            </div>
          </div>
        );

      case 1: // Children Information
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Your Children</h3>
              <Button
                onClick={handleAddChild}
                size="sm"
                className="bg-teal-600 hover:bg-teal-700"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Child
              </Button>
            </div>

            <ScrollArea className="h-[300px] pr-4">
              <div className="space-y-4">
                {localData.children.map((child: any, index: number) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="border border-teal-200 rounded-lg p-4 bg-white/50"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="font-medium text-gray-700">Child {index + 1}</h4>
                      <Button
                        onClick={() => handleRemoveChild(index)}
                        size="sm"
                        variant="ghost"
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div className="space-y-1">
                        <Label className="text-xs">Name</Label>
                        <Input
                          placeholder="Child's name"
                          value={child.name}
                          onChange={(e) => handleChildChange(index, 'name', e.target.value)}
                          className="h-9 text-sm"
                        />
                      </div>

                      <div className="space-y-1">
                        <Label className="text-xs">Age</Label>
                        <Input
                          type="number"
                          placeholder="Age"
                          value={child.age || ''}
                          onChange={(e) => handleChildChange(index, 'age', parseInt(e.target.value))}
                          className="h-9 text-sm"
                        />
                      </div>
                    </div>

                    <div className="mt-3 space-y-1">
                      <Label className="text-xs flex items-center gap-1">
                        <Heart className="h-3 w-3 text-pink-500" />
                        Interests (Optional)
                      </Label>
                      <Input
                        placeholder="e.g., Soccer, Reading, Art"
                        value={child.interests?.join(', ') || ''}
                        onChange={(e) => handleChildChange(index, 'interests', e.target.value.split(',').map((s: string) => s.trim()))}
                        className="h-9 text-sm"
                      />
                    </div>
                  </motion.div>
                ))}

                {localData.children.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Calendar className="h-12 w-12 mx-auto mb-3 text-teal-300" />
                    <p className="text-sm">No children added yet</p>
                    <p className="text-xs mt-1">Click "Add Child" to get started</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        );

      default:
        return (
          <div className="text-center py-8">
            <Sparkles className="h-12 w-12 mx-auto mb-3 text-teal-500" />
            <p className="text-gray-600 dark:text-gray-300">Continue to the next step</p>
          </div>
        );
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* User Information Form */}
      <div className="flex-1 bg-white dark:bg-gray-900 rounded-lg border-2 border-teal-200 dark:border-teal-600 p-8 mb-6 min-h-0 overflow-y-auto">
        <h3 className="text-lg font-bold mb-4 text-gray-800 dark:text-white">User Information</h3>
        {renderStepContent()}
      </div>


      {/* Streamed Information Display */}
      <div className="flex-shrink-0 h-48 bg-gradient-to-br from-teal-50 to-cyan-50 dark:from-gray-700 dark:to-gray-800 rounded-lg p-4 border border-teal-200 dark:border-gray-600">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Sparkles className="h-4 w-4 text-teal-600 dark:text-teal-400" />
            <p className="text-sm font-semibold text-gray-700 dark:text-gray-300">
              Streamed in Real-Time and shown dynamically in Mae's interactive UI
            </p>
          </div>
        </div>
        
        <div 
          ref={scrollRef}
          className="bg-white/70 dark:bg-gray-900/70 rounded p-3 h-28 overflow-y-auto text-xs space-y-1 font-mono"
        >
          <AnimatePresence>
            {streamedInfo.map((info, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="text-gray-600 dark:text-gray-300"
              >
                {'>'} {info}
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}