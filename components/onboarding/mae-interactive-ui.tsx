'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Plus, Trash2, User, Mail, Phone, MapPin, Calendar, Heart, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  aguiEventListener, 
  AG_UI_EVENTS, 
  type FillUserFormPayload,
  type AddFamilyMemberPayload,
  type EventResponse
} from '@/lib/ag-ui-events';
import { useCopilotAction } from '@copilotkit/react-core';

type OnboardingStep = 'user_info' | 'family_info' | 'complete';

interface MaeInteractiveUIProps {
  currentStep: OnboardingStep;
  onboardingData: any;
  onDataUpdate: (data: any) => void;
  onStepChange?: (step: OnboardingStep) => void;
}

export function MaeInteractiveUI({
  currentStep,
  onboardingData,
  onDataUpdate,
  onStepChange
}: MaeInteractiveUIProps) {
  const [localData, setLocalData] = useState(onboardingData);

  useEffect(() => {
    setLocalData(onboardingData);
  }, [onboardingData]);

  // Mae's CopilotKit Actions - This is the proper AG-UI integration!
  useCopilotAction({
    name: "fillUserField",
    description: "Fill a specific field in the user profile form during onboarding. Mae uses this to update user information in real-time as the user speaks.",
    parameters: [
      {
        name: "field",
        type: "string", 
        description: "The field to update (name, email, phone, location, role, etc.)",
        required: true,
      },
      {
        name: "value",
        type: "string",
        description: "The value to set for the field",
        required: true,
      }
    ],
    handler: async ({ field, value }: { field: string; value: string }) => {
      console.log(`🔥 Mae CopilotKit Action: Filling field ${field} with value:`, value);
      
      // Map fields to match our form structure
      const fieldMappings: Record<string, string> = {
        'name': 'name',
        'full_name': 'name',
        'display_name': 'name', 
        'first_name': 'name',
        'last_name': 'name',
        'email': 'email',
        'phone': 'phone',
        'phone_number': 'phone',
        'location': 'location',
        'zip': 'location',
        'zip_code': 'location',
        'role': 'role'
      };

      const targetField = fieldMappings[field.toLowerCase()] || field;
      
      if (targetField in localData.parentInfo) {
        handleParentInfoChange(targetField, value);
        return `Successfully updated ${targetField} to: ${value}`;
      }

      return `Field ${field} not found in user profile form`;
    },
  });

  useCopilotAction({
    name: "addFamilyMember", 
    description: "Add a new family member (child, spouse, etc.) to the user profile. Mae uses this when user mentions family members during conversation.",
    parameters: [
      {
        name: "name",
        type: "string",
        description: "The family member's name",
        required: true,
      },
      {
        name: "relationship",
        type: "string", 
        description: "Relationship to user (child, spouse, daughter, son, etc.)",
        required: true,
      },
      {
        name: "dateOfBirth", 
        type: "string",
        description: "Date of birth in YYYY-MM-DD format (optional)",
        required: false,
      },
      {
        name: "age",
        type: "number",
        description: "Age in years (optional)",
        required: false,
      },
      {
        name: "gender",
        type: "string", 
        description: "Gender (male, female, non-binary, etc.)",
        required: false,
      }
    ],
    handler: async ({ name, relationship, dateOfBirth, age, gender }: { 
      name: string; 
      relationship: string; 
      dateOfBirth?: string; 
      age?: number; 
      gender?: string; 
    }) => {
      console.log(`🔥 Mae CopilotKit Action: Adding family member`, { name, relationship, dateOfBirth, age, gender });
      
      // Calculate date of birth from age if needed
      let finalDateOfBirth = dateOfBirth;
      if (!finalDateOfBirth && age) {
        const currentYear = new Date().getFullYear();
        const birthYear = currentYear - age;
        finalDateOfBirth = `${birthYear}-01-01`;
      }

      const familyMember = {
        name,
        relationship,
        dateOfBirth: finalDateOfBirth || '',
        age: age || 0,
        gender: gender || '',
        medicalConditions: '',
        allergies: '',
        medications: '',
        notes: ''
      };

      // Add to local state
      const newFamilyMembers = [...(localData.familyMembers || []), familyMember];
      setLocalData((prev: any) => ({ ...prev, familyMembers: newFamilyMembers }));
      onDataUpdate({ ...localData, familyMembers: newFamilyMembers });

      return `Successfully added family member: ${name} (${relationship})${age ? `, age ${age}` : ''}`;
    },
  });

  useCopilotAction({
    name: "moveToNextStep",
    description: "Move to the next step in the onboarding process. Mae uses this to guide the user through the onboarding flow.",
    parameters: [
      {
        name: "step",
        type: "string",
        description: "The step to move to (user_info, family_info, complete)",
        required: true,
      }
    ],
    handler: async ({ step }: { step: string }) => {
      console.log(`🔥 Mae CopilotKit Action: Moving to step:`, step);

      // Use the parent component's step change handler
      if (onStepChange && (step === 'user_info' || step === 'family_info' || step === 'complete')) {
        onStepChange(step as OnboardingStep);
        return `Successfully moved to ${step} step`;
      }

      return `Step transition to ${step} acknowledged. The onboarding flow will handle the navigation.`;
    },
  });

  useCopilotAction({
    name: "buildFamilyGraph",
    description: "Build and display the family graph in real-time. Mae uses this when users ask to see the family graph or when building it dynamically during conversation.",
    parameters: [
      {
        name: "parentName",
        type: "string",
        description: "The parent/user's name",
        required: true,
      },
      {
        name: "familyMembers",
        type: "string",
        description: "JSON string of family members array with their details",
        required: false,
      }
    ],
    render: ({ status, args }) => {
      if (status === 'inProgress') {
        return (
          <div className="flex items-center gap-2 p-4 bg-teal-50 dark:bg-teal-900/20 rounded-lg">
            <img
              src="/OKdarkTsp.png"
              alt="Our Kidz Logo"
              className="h-4 w-4 object-contain animate-pulse"
            />
            <span className="text-sm text-teal-700 dark:text-teal-300">
              Building family graph for {args.parentName}...
            </span>
          </div>
        );
      }

      // Parse family members
      let membersList: Array<{
        name: string;
        relationship: string;
        age?: number;
        dateOfBirth?: string;
        gender?: string;
      }> = [];

      if (args.familyMembers) {
        try {
          membersList = JSON.parse(args.familyMembers);
        } catch (error) {
          console.warn('Failed to parse family members JSON:', error);
        }
      }

      return (
        <div className="p-4 bg-white dark:bg-gray-800 rounded-lg border border-teal-200 dark:border-teal-600">
          <div className="flex items-center gap-2 mb-4">
            <User className="h-5 w-5 text-teal-600" />
            <h3 className="font-semibold text-gray-800 dark:text-white">
              Family Graph for {args.parentName}
            </h3>
          </div>

          <div className="space-y-3">
            {/* Parent Node */}
            <div className="flex items-center gap-3 p-3 bg-teal-100 dark:bg-teal-900/30 rounded-lg">
              <User className="h-6 w-6 text-teal-600" />
              <div>
                <div className="font-medium text-gray-800 dark:text-white">{args.parentName}</div>
                <div className="text-sm text-gray-600 dark:text-gray-300">Parent</div>
              </div>
            </div>

            {/* Family Members */}
            {membersList.length > 0 && (
              <div className="ml-6 space-y-2">
                {membersList.map((member, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                    <Heart className="h-5 w-5 text-purple-600" />
                    <div>
                      <div className="font-medium text-gray-800 dark:text-white">{member.name}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        {member.relationship}{member.age ? `, age ${member.age}` : ''}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {membersList.length === 0 && (
              <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                <p className="text-sm">No family members added yet</p>
                <p className="text-xs mt-1">Tell Mae about your family members to see them here!</p>
              </div>
            )}
          </div>
        </div>
      );
    },
    handler: async ({ parentName, familyMembers }: {
      parentName: string;
      familyMembers?: string;
    }) => {
      console.log(`🔥 Mae CopilotKit Action: Building family graph for ${parentName}`, familyMembers);

      // Update parent info
      const updatedParentInfo = { ...localData.parentInfo, name: parentName };

      // Process family members from JSON string
      let membersList: Array<{
        name: string;
        relationship: string;
        age?: number;
        dateOfBirth?: string;
        gender?: string;
      }> = [];

      if (familyMembers) {
        try {
          membersList = JSON.parse(familyMembers);
        } catch (error) {
          console.warn('Failed to parse family members JSON:', error);
        }
      }

      const processedMembers = membersList.map(member => ({
        name: member.name,
        relationship: member.relationship,
        age: member.age || 0,
        dateOfBirth: member.dateOfBirth || '',
        gender: member.gender || '',
        medicalConditions: '',
        allergies: '',
        medications: '',
        notes: ''
      }));

      // Update local state with new family data
      const updatedData = {
        ...localData,
        parentInfo: updatedParentInfo,
        familyMembers: processedMembers
      };

      setLocalData(updatedData);
      onDataUpdate(updatedData);

      // Generate a summary of the family graph
      const memberSummary = processedMembers.length > 0 
        ? processedMembers.map(m => `${m.name} (${m.relationship}${m.age ? `, age ${m.age}` : ''})`).join(', ')
        : 'no family members yet';

      return `Successfully built family graph for ${parentName} with ${memberSummary}. The interactive family graph is now displayed above and will update in real-time as you add more family members.`;
    },
  });

  useCopilotAction({
    name: "addFamilyMember",
    description: "Add a single family member to the family graph. Mae uses this when users mention a specific family member during conversation.",
    parameters: [
      {
        name: "name",
        type: "string",
        description: "The family member's name",
        required: true,
      },
      {
        name: "relationship",
        type: "string",
        description: "Relationship to the parent (e.g., 'child', 'spouse', 'parent', 'sibling')",
        required: true,
      },
      {
        name: "age",
        type: "number",
        description: "Age of the family member",
        required: false,
      },
      {
        name: "gender",
        type: "string",
        description: "Gender of the family member",
        required: false,
      }
    ],
    render: ({ status, args }) => {
      if (status === 'inProgress') {
        return (
          <div className="flex items-center gap-2 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <Plus className="h-4 w-4 text-purple-600 animate-pulse" />
            <span className="text-sm text-purple-700 dark:text-purple-300">
              Adding {args.name} to family graph...
            </span>
          </div>
        );
      }

      return (
        <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-600">
          <div className="flex items-center gap-2 mb-2">
            <CheckCircle className="h-4 w-4 text-purple-600" />
            <span className="text-sm font-medium text-purple-700 dark:text-purple-300">
              Added to Family
            </span>
          </div>
          <div className="flex items-center gap-3">
            <Heart className="h-5 w-5 text-purple-600" />
            <div>
              <div className="font-medium text-gray-800 dark:text-white">{args.name}</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">
                {args.relationship}{args.age ? `, age ${args.age}` : ''}
              </div>
            </div>
          </div>
        </div>
      );
    },
    handler: async ({ name, relationship, age, gender }: {
      name: string;
      relationship: string;
      age?: number;
      gender?: string;
    }) => {
      console.log(`🔥 Mae CopilotKit Action: Adding family member ${name} (${relationship})`);

      const newMember = {
        name,
        relationship,
        age: age || 0,
        dateOfBirth: '',
        gender: gender || '',
        medicalConditions: '',
        allergies: '',
        medications: '',
        notes: ''
      };

      // Add to family members array
      const updatedFamilyMembers = [...(localData.familyMembers || []), newMember];

      const updatedData = {
        ...localData,
        familyMembers: updatedFamilyMembers
      };

      setLocalData(updatedData);
      onDataUpdate(updatedData);

      return `Successfully added ${name} (${relationship}${age ? `, age ${age}` : ''}) to the family graph. The family graph now shows ${updatedFamilyMembers.length} family member${updatedFamilyMembers.length !== 1 ? 's' : ''}.`;
    },
  });

  useCopilotAction({
    name: "showFamilyGraph",
    description: "Display the current family graph. Mae uses this when users specifically ask to see or view the family graph.",
    parameters: [],
    handler: async () => {
      console.log(`🔥 Mae CopilotKit Action: Showing current family graph`);
      
      const parentName = localData.parentInfo.name || 'User';
      const familyCount = (localData.familyMembers || []).length;
      const childrenCount = (localData.children || []).length;
      const totalMembers = familyCount + childrenCount;

      if (totalMembers === 0) {
        return `The family graph shows ${parentName} with no family members added yet. You can add family members by telling me about them, like "I have a 5-year-old daughter named Emma."`;
      }

      const membersList = [
        ...(localData.familyMembers || []).map((m: { name: any; relationship: any; age: any; }) => `${m.name} (${m.relationship}${m.age ? `, age ${m.age}` : ''})`),
        ...(localData.children || []).map((c: { name: any; age: any; }) => `${c.name} (child, age ${c.age})`)
      ];

      return `The family graph shows ${parentName} connected to ${totalMembers} family member${totalMembers > 1 ? 's' : ''}: ${membersList.join(', ')}. You can see the visual graph on the right side of the screen with color-coded relationships.`;
    },
  });

  // AG-UI Event Handlers for Mae integration
  const handleFillUserForm = async (payload: FillUserFormPayload): Promise<EventResponse> => {
    console.log('🔥 V2: Mae filling user form:', payload);
    
    const fieldMappings: Record<string, string> = {
      'name': 'name',
      'email': 'email', 
      'phone': 'phone',
      'zip': 'location'
    };

    const targetField = fieldMappings[payload.field] || payload.field;
    
    if (targetField in localData.parentInfo) {
      handleParentInfoChange(targetField, payload.value as string);
      return { success: true, message: `Updated ${targetField}` };
    }

    return { success: false, error: 'Unknown field' };
  };

  const handleAddFamilyMember = async (payload: AddFamilyMemberPayload): Promise<EventResponse> => {
    console.log('🔥 V2: Mae adding family member:', payload);
    
    const birthYear = new Date().getFullYear() - Math.floor((Date.now() - new Date(payload.date_of_birth).getTime()) / (365.25 * 24 * 60 * 60 * 1000));
    const age = new Date().getFullYear() - birthYear;
    
    const newChild = {
      name: payload.name,
      age: age,
      grade: payload.relationship === 'child' ? `Grade ${Math.max(1, age - 5)}` : undefined,
      interests: []
    };

    const newData = {
      ...localData,
      children: [...localData.children, newChild]
    };

    setLocalData(newData);
    onDataUpdate(newData);
    
    return { success: true, message: `Added ${payload.name}` };
  };

  // Register AG-UI event listeners + Direct UI Bridge + Force UI System
  useEffect(() => {
    console.log('🔥 V2: Registering Mae AG-UI listeners with triple redundancy');
    
    // AG-UI Event System (Primary)
    aguiEventListener.addEventListener(AG_UI_EVENTS.FILL_USER_FORM, handleFillUserForm);
    aguiEventListener.addEventListener(AG_UI_EVENTS.ADD_FAMILY_MEMBER, handleAddFamilyMember);
    
    // Direct UI Bridge (Secondary - Backup)
    // const setupDirectUIBridge = async () => {
    //   const { maeUIBridge } = await import('@/lib/mae-direct-ui-bridge');
    //   console.log('🔥 V2: Connected to Direct UI Bridge') ;
    //   maeUIBridge.subscribe('fillUserField', (field: string, value: any) => {
    //     console.log('🔥 V2: Direct UI Bridge update:', { field, value });
    //     handleParentInfoChange(field, value);
    //   });
      
    //   maeUIBridge.subscribe('addFamilyMember', (member: any) => {
    //     console.log('🔥 V2: Direct UI Bridge add family member:', member);
    //     const birthYear = new Date().getFullYear() - Math.floor((Date.now() - new Date(member.date_of_birth).getTime()) / (365.25 * 24 * 60 * 60 * 1000));
    //     const age = new Date().getFullYear() - birthYear;
        
    //     const newChild = {
    //       name: member.name,
    //       age: age,
    //       grade: member.relationship === 'child' ? `Grade ${Math.max(1, age - 5)}` : undefined,
    //       interests: []
    //     };
        
    //     const newData = {
    //       ...localData,
    //       children: [...localData.children, newChild]
    //     };
        
    //     setLocalData(newData);
    //     onDataUpdate(newData);
    //   }
    
    // setupDirectUIBridge();
    
    // Force UI System (Tertiary - Ultimate Backup)
    const handleForceUIUpdate = (event: any) => {
      console.log('🔥 V2: Force UI System update:', event.detail);
      const { action, data } = event.detail;
      
      if (action === 'fill_field') {
        handleParentInfoChange(data.field, data.value);
      } else if (action === 'add_family_member') {
        const member = data;
        const birthYear = new Date().getFullYear() - Math.floor((Date.now() - new Date(member.date_of_birth).getTime()) / (365.25 * 24 * 60 * 60 * 1000));
        const age = new Date().getFullYear() - birthYear;
        
        const newChild = {
          name: member.name,
          age: age,
          grade: member.relationship === 'child' ? `Grade ${Math.max(1, age - 5)}` : undefined,
          interests: []
        };
        
        const newData = {
          ...localData,
          children: [...localData.children, newChild]
        };
        
        setLocalData(newData);
        onDataUpdate(newData);
      }
    };
    
    window.addEventListener('FORCE_MAE_UI_UPDATE', handleForceUIUpdate);
    
    return () => {
      console.log('🔥 V2: Cleaning up Mae AG-UI listeners');
      aguiEventListener.removeEventListener(AG_UI_EVENTS.FILL_USER_FORM);
      aguiEventListener.removeEventListener(AG_UI_EVENTS.ADD_FAMILY_MEMBER);
      window.removeEventListener('FORCE_MAE_UI_UPDATE', handleForceUIUpdate);
    };
  }, [localData, onDataUpdate]);

  const handleParentInfoChange = (field: string, value: string) => {
    const newData = {
      ...localData,
      parentInfo: {
        ...localData.parentInfo,
        [field]: value
      }
    };
    setLocalData(newData);
    onDataUpdate(newData);
  };

  const handleAddChild = () => {
    const newData = {
      ...localData,
      children: [
        ...localData.children,
        { name: '', age: 0, interests: [] }
      ]
    };
    setLocalData(newData);
    onDataUpdate(newData);
  };

  const handleChildChange = (index: number, field: string, value: any) => {
    const newChildren = [...localData.children];
    newChildren[index] = {
      ...newChildren[index],
      [field]: value
    };
    const newData = {
      ...localData,
      children: newChildren
    };
    setLocalData(newData);
    onDataUpdate(newData);
  };

  const handleRemoveChild = (index: number) => {
    const newChildren = localData.children.filter((_: any, i: number) => i !== index);
    const newData = {
      ...localData,
      children: newChildren
    };
    setLocalData(newData);
    onDataUpdate(newData);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'user_info': // Parent Information
        return (
          <div className="space-y-6">
            <div className="space-y-3">
              <Label htmlFor="parent-name" className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                <User className="h-4 w-4 text-teal-600 dark:text-teal-400" />
                Your Name
              </Label>
              <Input
                id="parent-name"
                placeholder="Enter your name"
                value={localData.parentInfo.name}
                onChange={(e) => handleParentInfoChange('name', e.target.value)}
                className="border-teal-200 focus:border-teal-400 dark:border-teal-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400"
              />
            </div>

            <div className="space-y-3">
              <Label htmlFor="parent-email" className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                <Mail className="h-4 w-4 text-teal-600 dark:text-teal-400" />
                Email Address
              </Label>
              <Input
                id="parent-email"
                type="email"
                placeholder="<EMAIL>"
                value={localData.parentInfo.email}
                onChange={(e) => handleParentInfoChange('email', e.target.value)}
                className="border-teal-200 focus:border-teal-400 dark:border-teal-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400"
              />
            </div>

            <div className="space-y-3">
              <Label htmlFor="parent-phone" className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                <Phone className="h-4 w-4 text-teal-600 dark:text-teal-400" />
                Phone Number (Optional)
              </Label>
              <Input
                id="parent-phone"
                type="tel"
                placeholder="(*************"
                value={localData.parentInfo.phone || ''}
                onChange={(e) => handleParentInfoChange('phone', e.target.value)}
                className="border-teal-200 focus:border-teal-400 dark:border-teal-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400"
              />
            </div>

            <div className="space-y-3">
              <Label htmlFor="parent-location" className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                <MapPin className="h-4 w-4 text-teal-600 dark:text-teal-400" />
                Location (Optional)
              </Label>
              <Input
                id="parent-location"
                placeholder="City, State"
                value={localData.parentInfo.location || ''}
                onChange={(e) => handleParentInfoChange('location', e.target.value)}
                className="border-teal-200 focus:border-teal-400 dark:border-teal-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400"
              />
            </div>
          </div>
        );

      case 'family_info': // Children Information
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Your Children</h3>
              <Button
                onClick={handleAddChild}
                size="sm"
                className="bg-teal-600 hover:bg-teal-700"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Child
              </Button>
            </div>

            <ScrollArea className="h-[300px] pr-4">
              <div className="space-y-4">
                {localData.children.map((child: any, index: number) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="border border-teal-200 rounded-lg p-4 bg-white/50"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="font-medium text-gray-700">Child {index + 1}</h4>
                      <Button
                        onClick={() => handleRemoveChild(index)}
                        size="sm"
                        variant="ghost"
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div className="space-y-1">
                        <Label className="text-xs">Name</Label>
                        <Input
                          placeholder="Child's name"
                          value={child.name}
                          onChange={(e) => handleChildChange(index, 'name', e.target.value)}
                          className="h-9 text-sm"
                        />
                      </div>

                      <div className="space-y-1">
                        <Label className="text-xs">Age</Label>
                        <Input
                          type="number"
                          placeholder="Age"
                          value={child.age || ''}
                          onChange={(e) => handleChildChange(index, 'age', parseInt(e.target.value))}
                          className="h-9 text-sm"
                        />
                      </div>
                    </div>

                    <div className="mt-3 space-y-1">
                      <Label className="text-xs flex items-center gap-1">
                        <Heart className="h-3 w-3 text-pink-500" />
                        Interests (Optional)
                      </Label>
                      <Input
                        placeholder="e.g., Soccer, Reading, Art"
                        value={child.interests?.join(', ') || ''}
                        onChange={(e) => handleChildChange(index, 'interests', e.target.value.split(',').map((s: string) => s.trim()))}
                        className="h-9 text-sm"
                      />
                    </div>
                  </motion.div>
                ))}

                {localData.children.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Calendar className="h-12 w-12 mx-auto mb-3 text-teal-300" />
                    <p className="text-sm">No children added yet</p>
                    <p className="text-xs mt-1">Click "Add Child" to get started</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        );

      case 'complete': // Completion
        return (
          <div className="text-center py-12">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", duration: 0.6 }}
            >
              <CheckCircle className="h-16 w-16 mx-auto mb-4 text-green-500" />
            </motion.div>
            <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
              Welcome to Our Kidz!
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Your profile is complete. Mae is ready to help you with personalized parenting guidance.
            </p>
            <div className="bg-teal-50 dark:bg-teal-900/20 rounded-lg p-4 max-w-md mx-auto">
              <p className="text-sm text-teal-700 dark:text-teal-300">
                🎉 You can now start chatting with Mae for personalized advice and support!
              </p>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center py-8">
            <img
              src="/OKdarkTsp.png"
              alt="Our Kidz Logo"
              className="h-12 w-12 mx-auto mb-3 object-contain"
            />
            <p className="text-gray-600 dark:text-gray-300">Continue to the next step</p>
          </div>
        );
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* User Information Form - Now takes full height */}
      <div className="flex-1 bg-white dark:bg-gray-900 rounded-lg border-2 border-teal-200 dark:border-teal-600 p-8 min-h-0 overflow-y-auto">
        <div className="flex items-center gap-2 mb-6">
          <img
            src="/OKdarkTsp.png"
            alt="Our Kidz Logo"
            className="h-6 w-6 object-contain"
          />
          <h3 className="text-xl text-gray-800 dark:text-white">
            {currentStep === 'user_info' ? 'Tell Mae About Yourself' :
             currentStep === 'family_info' ? 'Tell Mae About Your Family' :
             'Welcome to Our Kidz!'}
          </h3>
        </div>

        {currentStep !== 'complete' && (
          <div className="mb-6 p-4 bg-teal-50 dark:bg-teal-900/20 rounded-lg border border-teal-200 dark:border-teal-700">
            <p className="text-sm text-teal-700 dark:text-teal-300">
              💬 <strong>Talk to Mae:</strong> You can speak naturally to Mae and she'll fill in your information automatically.
              Try saying things like "My name is John" or "I live in New York" and watch the fields update in real-time!
            </p>
          </div>
        )}

        {renderStepContent()}
      </div>
    </div>
  );
}