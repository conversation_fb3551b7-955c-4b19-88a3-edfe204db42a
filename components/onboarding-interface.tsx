"use client";

import React, { useState, useEffect, useC<PERSON>back, useReducer } from "react";
import { useUser } from "@clerk/nextjs";
import { motion, AnimatePresence } from "framer-motion";
import confetti from "canvas-confetti";
import {
  User,
  Users,
  CheckCircle,
  AlertCircle,
  Sparkles,
  ArrowRight,
  Plus,
  Trash2,
  Mic,
  Edit,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { cn } from "@/lib/utils";
import {
  aguiEventListener,
  AG_UI_EVENTS,
  OnboardingValidator,
  type FillUserFormPayload,
  type SubmitUserFormPayload,
  type AddFamilyMemberPayload,
  type UpdateFamilyMemberPayload,
  type EventResponse,
} from "@/lib/ag-ui-events";

// --- DATA & STATE ---
type UserFormData = {
  email: string;
  fullName: string;
  role: "parent" | "guardian" | "caregiver";
  phoneNumber: string;
  zipCode: string;
};
type FamilyMemberData = {
  id: string;
  name: string;
  dateOfBirth: string;
  relationship: string;
};
type OnboardingStep = "user_info" | "family_info" | "complete";

const USER_FORM_FIELDS: (keyof UserFormData)[] = [
  "fullName",
  "email",
  "phoneNumber",
  "zipCode",
  "role",
];

// Enhanced state to handle editing mode and visible fields
type OnboardingState = {
  userForm: UserFormData;
  familyMembers: FamilyMemberData[];
  validationErrors: Record<string, string>;
  isSubmitting: boolean;
  visibleFields: Set<keyof UserFormData>;
  isEditing: boolean;
  maeFailedAttempts: number;
};

type OnboardingAction =
  | { type: "SET_USER_FORM_DATA"; payload: Partial<UserFormData> }
  | { type: "ADD_FAMILY_MEMBER"; payload: FamilyMemberData }
  | { type: "REMOVE_FAMILY_MEMBER"; payload: { id: string } }
  | { type: "SET_VALIDATION_ERRORS"; payload: Record<string, string> }
  | { type: "CLEAR_VALIDATION_ERRORS" }
  | { type: "SET_SUBMITTING"; payload: boolean }
  | { type: "REVEAL_FIELD"; payload: keyof UserFormData }
  | { type: "TOGGLE_EDITING"; payload: boolean }
  | { type: "INCREMENT_MAE_FAILED_ATTEMPTS" };

const initialState: OnboardingState = {
  userForm: {
    email: "",
    fullName: "",
    role: "parent",
    phoneNumber: "",
    zipCode: "",
  },
  familyMembers: [],
  validationErrors: {},
  isSubmitting: false,
  visibleFields: new Set(),
  isEditing: false,
  maeFailedAttempts: 0,
};

function onboardingReducer(
  state: OnboardingState,
  action: OnboardingAction,
): OnboardingState {
  switch (action.type) {
    case "SET_USER_FORM_DATA":
      return { ...state, userForm: { ...state.userForm, ...action.payload } };
    case "ADD_FAMILY_MEMBER":
      return {
        ...state,
        familyMembers: [...state.familyMembers, action.payload],
      };
    case "REMOVE_FAMILY_MEMBER":
      return {
        ...state,
        familyMembers: state.familyMembers.filter(
          (m) => m.id !== action.payload.id,
        ),
      };
    case "SET_VALIDATION_ERRORS":
      return { ...state, validationErrors: action.payload };
    case "CLEAR_VALIDATION_ERRORS":
      return { ...state, validationErrors: {} };
    case "SET_SUBMITTING":
      return { ...state, isSubmitting: action.payload };
    case "REVEAL_FIELD":
      return {
        ...state,
        visibleFields: new Set(state.visibleFields).add(action.payload),
      };
    case "TOGGLE_EDITING":
      return { ...state, isEditing: action.payload };
    case "INCREMENT_MAE_FAILED_ATTEMPTS":
      return { ...state, maeFailedAttempts: state.maeFailedAttempts + 1 };
    default:
      return state;
  }
}

// --- HOOKS ---
function useOnboardingState() {
  const [state, dispatch] = useReducer(onboardingReducer, initialState);
  const { user, isLoaded } = useUser();

  useEffect(() => {
    if (isLoaded && user) {
      const userData = {
        email: user.primaryEmailAddress?.emailAddress || "",
        fullName: user.fullName || "",
      };
      dispatch({ type: "SET_USER_FORM_DATA", payload: userData });
      if (userData.email) dispatch({ type: "REVEAL_FIELD", payload: "email" });
      if (userData.fullName)
        dispatch({ type: "REVEAL_FIELD", payload: "fullName" });
    }
  }, [user, isLoaded]);

  return { state, dispatch };
}

function useAguiOnboardingEvents(dispatch: React.Dispatch<OnboardingAction>) {
  const handleFillUserForm = useCallback(
    async (payload: FillUserFormPayload): Promise<EventResponse> => {
      const field = Object.keys(payload)[0] as keyof UserFormData;
      if (field) {
        dispatch({ type: "REVEAL_FIELD", payload: field });
        dispatch({ type: "SET_USER_FORM_DATA", payload });
      }
      return { success: true, message: `Updated user form.` };
    },
    [dispatch],
  );

  const handleSubmitUserForm = useCallback(async (): Promise<EventResponse> => {
    document.getElementById("user-form-submit-button")?.click();
    return { success: true, message: "User form submission triggered." };
  }, []);

  const handleAddFamilyMember = useCallback(
    async (payload: AddFamilyMemberPayload): Promise<EventResponse> => {
      try {
        const response = await fetch("/api/onboarding/family", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payload),
        });
        if (!response.ok) {
          throw new Error("Failed to add family member to database");
        }
        const newMember = await response.json();
        dispatch({ type: "ADD_FAMILY_MEMBER", payload: newMember[0] });
        return {
          success: true,
          message: `Added family member ${payload.name}.`,
        };
      } catch (error) {
        console.error(error);
        // This is where we would ideally have a more robust error handling from Mae
        dispatch({ type: "INCREMENT_MAE_FAILED_ATTEMPTS" });
        return { success: false, error: "Failed to add family member." };
      }
    },
    [dispatch],
  );

  useEffect(() => {
    const listeners: Record<string, (payload: any) => Promise<EventResponse>> =
      {
        [AG_UI_EVENTS.FILL_USER_FORM]: handleFillUserForm,
        [AG_UI_EVENTS.SUBMIT_USER_FORM]: handleSubmitUserForm,
        [AG_UI_EVENTS.ADD_FAMILY_MEMBER]: handleAddFamilyMember,
      };

    for (const [event, handler] of Object.entries(listeners))
      aguiEventListener.addEventListener(event, handler);
    return () => {
      for (const event of Object.keys(listeners))
        aguiEventListener.removeEventListener(event);
    };
  }, [handleFillUserForm, handleSubmitUserForm, handleAddFamilyMember]);
}

// --- UI COMPONENTS ---
const fieldVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const Slate: React.FC<{
  title: string;
  description: string;
  children: React.ReactNode;
}> = ({ title, description, children }) => (
  <div className="w-full p-8 border-2 border-dashed rounded-lg text-center">
    <Mic className="h-10 w-10 mx-auto mb-4 text-primary" />
    <h3 className="text-lg font-semibold text-foreground">{title}</h3>
    <p className="text-sm text-muted-foreground mb-6">{description}</p>
    <div className="text-left space-y-3 min-h-[100px]">{children}</div>
  </div>
);

const SlateItem: React.FC<{ label: string; value?: string }> = ({
  label,
  value,
}) => (
  <motion.div
    variants={fieldVariants}
    initial="hidden"
    animate="visible"
    exit="hidden"
    className="flex justify-between items-center p-3 bg-muted/50 rounded-md"
  >
    <span className="font-semibold text-muted-foreground">{label}</span>
    <span className="font-mono text-primary font-bold">{value || "..."}</span>
  </motion.div>
);

const FormField: React.FC<{
  name: string;
  label: string;
  error?: string;
  children: React.ReactNode;
}> = ({ name, label, error, children }) => (
  <motion.div
    variants={fieldVariants}
    initial="hidden"
    animate="visible"
    exit="hidden"
    className="space-y-2"
    ag-ui-id={`field-${name}`}
  >
    <Label
      htmlFor={name}
      className={cn("font-semibold", error && "text-destructive")}
    >
      {label}
    </Label>
    {children}
    <AnimatePresence>
      {error && (
        <motion.p
          initial={{ opacity: 0, y: -5 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-sm font-medium text-destructive flex items-center gap-1"
        >
          <AlertCircle className="h-4 w-4" /> {error}
        </motion.p>
      )}
    </AnimatePresence>
  </motion.div>
);

const UserForm: React.FC<{
  state: OnboardingState;
  dispatch: React.Dispatch<OnboardingAction>;
  onComplete: () => void;
}> = ({ state, dispatch, onComplete }) => {
  const { userForm, validationErrors, isSubmitting, visibleFields, isEditing } =
    state;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    dispatch({ type: "CLEAR_VALIDATION_ERRORS" });
    const validation = OnboardingValidator.validateUserData(userForm);
    if (!validation.valid) {
      const errors = validation.errors.reduce(
        (acc, err) => {
          const field = err
            .split(" ")[0]
            .toLowerCase()
            .replace("full", "fullName")
            .replace("phone", "phoneNumber")
            .replace("zip", "zipCode");
          acc[field] = err;
          return acc;
        },
        {} as Record<string, string>,
      );
      dispatch({ type: "SET_VALIDATION_ERRORS", payload: errors });
      return;
    }

    dispatch({ type: "SET_SUBMITTING", payload: true });
    try {
      await fetch("/api/onboarding/users", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(userForm),
      });
      onComplete();
    } catch (error) {
      dispatch({
        type: "SET_VALIDATION_ERRORS",
        payload: { form: "Submission failed." },
      });
    } finally {
      dispatch({ type: "SET_SUBMITTING", payload: false });
    }
  };

  const fieldLabels: Record<keyof UserFormData, string> = {
    fullName: "Full Name",
    email: "Email Address",
    phoneNumber: "Phone Number",
    zipCode: "Zip Code",
    role: "Your Role",
  };

  const EditableForm = (
    <motion.form
      key="form"
      onSubmit={handleSubmit}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="space-y-6 mt-8"
    >
      <FormField
        name="fullName"
        label="Full Name"
        error={validationErrors.fullName}
      >
        <Input
          id="fullName"
          placeholder="Jane Doe"
          value={userForm.fullName}
          onChange={(e) =>
            dispatch({
              type: "SET_USER_FORM_DATA",
              payload: { fullName: e.target.value },
            })
          }
        />
      </FormField>
      <FormField
        name="email"
        label="Email Address"
        error={validationErrors.email}
      >
        <Input
          id="email"
          type="email"
          placeholder="<EMAIL>"
          value={userForm.email}
          onChange={(e) =>
            dispatch({
              type: "SET_USER_FORM_DATA",
              payload: { email: e.target.value },
            })
          }
        />
      </FormField>
      <FormField
        name="phoneNumber"
        label="Phone Number"
        error={validationErrors.phoneNumber}
      >
        <Input
          id="phoneNumber"
          placeholder="(*************"
          value={userForm.phoneNumber}
          onChange={(e) =>
            dispatch({
              type: "SET_USER_FORM_DATA",
              payload: { phoneNumber: e.target.value },
            })
          }
        />
      </FormField>
      <FormField
        name="zipCode"
        label="Zip Code"
        error={validationErrors.zipCode}
      >
        <Input
          id="zipCode"
          placeholder="90210"
          value={userForm.zipCode}
          onChange={(e) =>
            dispatch({
              type: "SET_USER_FORM_DATA",
              payload: { zipCode: e.target.value },
            })
          }
        />
      </FormField>
      <FormField name="role" label="Your Role" error={validationErrors.role}>
        <Select
          value={userForm.role}
          onValueChange={(value: UserFormData["role"]) =>
            dispatch({ type: "SET_USER_FORM_DATA", payload: { role: value } })
          }
        >
          <SelectTrigger id="role">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="parent">Parent</SelectItem>
            <SelectItem value="guardian">Guardian</SelectItem>
            <SelectItem value="caregiver">Caregiver</SelectItem>
          </SelectContent>
        </Select>
      </FormField>
      <div className="flex gap-4 justify-end">
        <Button
          variant="ghost"
          onClick={() => dispatch({ type: "TOGGLE_EDITING", payload: false })}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting} size="lg">
          {isSubmitting ? "Saving..." : "Save & Continue"}
        </Button>
      </div>
    </motion.form>
  );

  const SlateView = (
    <motion.div
      key="slate"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="mt-8"
    >
      <Slate
        title="Listening for your details..."
        description='Say things like "My name is Jane Doe" or "My email is..."'
      >
        <AnimatePresence>
          {USER_FORM_FIELDS.map(
            (field) =>
              visibleFields.has(field) && (
                <SlateItem
                  key={field}
                  label={fieldLabels[field]}
                  value={userForm[field]}
                />
              ),
          )}
        </AnimatePresence>
      </Slate>
      <div className="flex justify-between items-center pt-4">
        <Button
          variant="outline"
          onClick={() => dispatch({ type: "TOGGLE_EDITING", payload: true })}
        >
          <Edit className="mr-2 h-4 w-4" /> Edit
        </Button>
        <Button
          id="user-form-submit-button"
          onClick={onComplete}
          size="lg"
          className="px-8 py-6 text-lg font-semibold rounded-full shadow-lg shadow-primary/20"
        >
          Continue
          <ArrowRight className="ml-2 h-5 w-5" />
        </Button>
      </div>
    </motion.div>
  );

  return (
    <Card
      ag-ui-id="user-info-form"
      className="w-full max-w-2xl bg-transparent border-none shadow-none"
    >
      <CardHeader className="text-center">
        <CardTitle className="text-4xl font-bold">Your Information</CardTitle>
        <CardDescription className="text-lg text-muted-foreground">
          Tell us a bit about yourself to get started.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <AnimatePresence mode="wait">
          {isEditing ? EditableForm : SlateView}
        </AnimatePresence>
      </CardContent>
    </Card>
  );
};

const FamilyForm: React.FC<{
  state: OnboardingState;
  dispatch: React.Dispatch<OnboardingAction>;
  onComplete: () => void;
}> = ({ state, dispatch, onComplete }) => {
  const { familyMembers, maeFailedAttempts } = state;
  const [newMember, setNewMember] = useState({
    name: "",
    dob: "",
    relationship: "",
  });

  const handleAddManual = async () => {
    if (newMember.name && newMember.dob && newMember.relationship) {
      try {
        const response = await fetch("/api/onboarding/family", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            name: newMember.name,
            dateOfBirth: newMember.dob,
            relationship: newMember.relationship,
          }),
        });
        if (!response.ok) {
          throw new Error("Failed to add family member to database");
        }
        const addedMember = await response.json();
        dispatch({ type: "ADD_FAMILY_MEMBER", payload: addedMember[0] });
        setNewMember({ name: "", dob: "", relationship: "" });
      } catch (error) {
        console.error(error);
        // Here you might want to show an error to the user
      }
    }
  };

  return (
    <Card
      ag-ui-id="family-info-form"
      className="w-full max-w-2xl bg-transparent border-none shadow-none"
    >
      <CardHeader className="text-center">
        <CardTitle className="text-4xl font-bold">Family Members</CardTitle>
        <CardDescription className="text-lg text-muted-foreground">
          Add your children or other family members. Mae can help with this.
        </CardDescription>
      </CardHeader>
      <CardContent className="mt-8">
        <div className="space-y-4 mb-8 min-h-[150px]">
          <AnimatePresence>
            {familyMembers.length === 0 && (
              <Slate
                title="Listening for family members..."
                description='You can say, "Add my son, John, born on..."'
              >
                {" "}
                <div />{" "}
              </Slate>
            )}
            {familyMembers.map((member) => (
              <motion.div
                key={member.id}
                layout
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="flex items-center justify-between p-4 bg-background rounded-lg border"
              >
                <div className="flex items-center gap-4">
                  <div className="bg-primary/10 text-primary p-3 rounded-full">
                    <Users className="h-5 w-5" />
                  </div>
                  <div>
                    <p className="font-semibold text-base">{member.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {member.relationship} &bull; Born {member.dateOfBirth}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() =>
                    dispatch({
                      type: "REMOVE_FAMILY_MEMBER",
                      payload: { id: member.id },
                    })
                  }
                >
                  <Trash2 className="h-4 w-4 text-muted-foreground hover:text-destructive" />
                </Button>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
        {maeFailedAttempts >= 5 && (
          <div className="pt-6 space-y-4 border-t">
            <h3 className="font-medium text-center text-muted-foreground">
              Add Manually
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
              <Input
                placeholder="Full Name"
                value={newMember.name}
                onChange={(e) =>
                  setNewMember((s) => ({ ...s, name: e.target.value }))
                }
              />
              <Input
                type="date"
                placeholder="Date of Birth"
                value={newMember.dob}
                onChange={(e) =>
                  setNewMember((s) => ({ ...s, dob: e.target.value }))
                }
              />
              <Input
                placeholder="Relationship (e.g., Son)"
                value={newMember.relationship}
                onChange={(e) =>
                  setNewMember((s) => ({ ...s, relationship: e.target.value }))
                }
              />
            </div>
            <div className="flex justify-end">
              <Button variant="outline" onClick={handleAddManual}>
                <Plus className="mr-2 h-4 w-4" /> Add Member
              </Button>
            </div>
          </div>
        )}
        <div className="flex justify-end pt-8 mt-8 border-t">
          <Button
            onClick={onComplete}
            size="lg"
            className="px-8 py-6 text-lg font-semibold rounded-full shadow-lg shadow-primary/20"
          >
            Finish Setup
            <CheckCircle className="ml-2 h-5 w-5" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

const CompletionScreen: React.FC = () => {
  useEffect(() => {
    confetti({
      particleCount: 150,
      spread: 90,
      origin: { y: 0.6 },
      zIndex: 1000,
    });
  }, []);

  return (
    <div
      className="text-center py-10 flex flex-col items-center justify-center"
      ag-ui-id="completion-screen"
    >
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1, rotate: -10 }}
        transition={{ type: "spring", delay: 0.2, stiffness: 150 }}
      >
        <Sparkles className="h-24 w-24 text-primary mx-auto mb-8" />
      </motion.div>
      <h2 className="text-4xl font-bold mb-2">Setup Complete!</h2>
      <p className="text-lg text-muted-foreground mb-8">
        Your family profile is ready. Welcome to Our Kidz!
      </p>
      <Button
        size="lg"
        onClick={() => (window.location.href = "/")}
        className="px-8 py-6 text-lg font-semibold rounded-full shadow-lg shadow-primary/20"
      >
        Go to Dashboard
      </Button>
    </div>
  );
};

// --- MAIN COMPONENT ---
interface OnboardingInterfaceProps {
  initialStep?: OnboardingStep;
  onComplete: () => void;
  isVoiceEnabled?: boolean;
}

export function OnboardingInterface({
  initialStep = "user_info",
  onComplete,
}: OnboardingInterfaceProps) {
  const { state, dispatch } = useOnboardingState();
  useAguiOnboardingEvents(dispatch);

  const renderCurrentStep = () => {
    switch (initialStep) {
      case "user_info":
        return (
          <UserForm state={state} dispatch={dispatch} onComplete={onComplete} />
        );
      case "family_info":
        return (
          <FamilyForm
            state={state}
            dispatch={dispatch}
            onComplete={onComplete}
          />
        );
      case "complete":
        return <CompletionScreen />;
      default:
        return null;
    }
  };

  return (
    <div
      className="w-full h-full flex items-center justify-center"
      ag-ui-id="onboarding-interface"
    >
      <AnimatePresence mode="wait">
        <motion.div
          key={initialStep}
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.3 }}
        >
          {renderCurrentStep()}
        </motion.div>
      </AnimatePresence>
    </div>
  );
}
