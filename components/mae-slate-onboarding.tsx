"use client"

import React, { useEffect, useState, useCallback } from 'react'
import { useUser } from '@clerk/nextjs'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  aguiEventListener, 
  AG_UI_EVENTS, 
  type FillUserFormPayload,
  type SubmitUserFormPayload,
  type AddFamilyMemberPayload,
  type OnboardingProgressPayload,
  type EventResponse
} from '@/lib/ag-ui-events'

interface UserFieldData {
  [key: string]: any
  email?: string
  name?: string
  role?: string
  phone?: string
  zip?: string
  date_of_birth?: string
  emergency_contact?: any
}

interface FamilyMemberData {
  name: string
  date_of_birth: string
  relationship: string
  gender?: string
  medical_conditions?: string[]
  allergies?: string[]
  medications?: any[]
  additional_notes?: string
}

type OnboardingStep = 'welcome' | 'user_info' | 'family_info' | 'complete'

export function MaeSlateOnboarding() {
  const { user } = useUser()
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('user_info')
  const [userFields, setUserFields] = useState<UserFieldData>({})
  const [familyMembers, setFamilyMembers] = useState<FamilyMemberData[]>([])
  const [maeMessages, setMaeMessages] = useState<string[]>([
    'Mae: Hello! I\'m ready to help you set up your profile. Tell me your information and I\'ll fill it in for you!'
  ])

  // DIRECT UI BRIDGE CONNECTION + FORCE UI SYSTEM
  useEffect(() => {
    // Connect to Direct UI Bridge
    import('@/lib/mae-direct-ui-bridge').then(({ maeUIBridge }) => {
      console.log('🔥 CONNECTING TO DIRECT UI BRIDGE')
      
      const unsubscribe = maeUIBridge.subscribe((state) => {
        console.log('🔥 DIRECT UI STATE UPDATE:', state)
        setUserFields(state.userFields)
        setFamilyMembers(state.familyMembers)
        setCurrentStep(state.currentStep)
        setMaeMessages([...state.messages])
        
        // Also notify parent page of step changes
        if (state.currentStep !== currentStep) {
          notifyStepChange(state.currentStep)
        }
      })
      
      // Initialize with current state
      const currentState = maeUIBridge.getState()
      if (Object.keys(currentState.userFields).length > 0 || currentState.familyMembers.length > 0) {
        setUserFields(currentState.userFields)
        setFamilyMembers(currentState.familyMembers)
        setCurrentStep(currentState.currentStep)
        setMaeMessages([...currentState.messages])
      }
      
      return unsubscribe
    })

    // ALSO CONNECT TO FORCE UI SYSTEM - FINAL BACKUP
    import('@/lib/mae-force-ui').then(({ subscribeToMaeUI, getMaeUIState }) => {
      console.log('🔥🔥🔥 CONNECTING TO FORCE MAE UI SYSTEM')
      
      const unsubscribe = subscribeToMaeUI((state) => {
        console.log('🔥🔥🔥 FORCE UI STATE UPDATE:', state)
        setUserFields(state.userFields)
        setFamilyMembers(state.familyMembers)
        setCurrentStep(state.currentStep as any)
        setMaeMessages([...state.messages])
        
        // Step change notification
        if (state.currentStep !== currentStep) {
          notifyStepChange(state.currentStep as any)
        }
      })
      
      // Get initial state
      const initialState = getMaeUIState()
      console.log('🔥🔥🔥 FORCE UI INITIAL STATE:', initialState)
      
      return unsubscribe
    })
  }, [])
  
  // Step change notification function
  const notifyStepChange = useCallback(async (step: OnboardingStep) => {
    console.log('🎯 Mae notifying step change to:', step)
    
    try {
      const { aguiEventDispatcher } = await import('@/lib/ag-ui-events')
      const result = await aguiEventDispatcher.updateProgress({ step })
      console.log('🎯 Step change notification result:', result)
    } catch (error) {
      console.warn('🚨 Failed to notify step change:', error)
    }
  }, [])

  const addMaeMessage = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    setMaeMessages(prev => [...prev, `${timestamp} - ${message}`])
  }, [])

  // AG-UI Event Handlers
  const handleFillUserForm = useCallback(async (payload: FillUserFormPayload, requestId: string): Promise<EventResponse> => {
    
    if (!payload.field || payload.value === undefined) {
      return {
        success: false,
        error: 'Invalid payload: field and value are required'
      }
    }

    try {
      // Update the user fields with Mae's data
      setUserFields(prev => {
        const updated = { ...prev, [payload.field]: payload.value }
        console.log('🎯 Updated user fields in UI:', updated)
        return updated
      })

      // Add Mae's message about the update
      const fieldName = payload.field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      addMaeMessage(`Mae: I've added your ${fieldName}: ${payload.value}`)

      // If we're getting user info, make sure we're on the right step
      if (currentStep === 'welcome') {
        setCurrentStep('user_info')
        
        // Trigger the main page step update via event dispatch with proper error handling
        try {
          const { aguiEventDispatcher } = await import('@/lib/ag-ui-events')
          const progressResult = await aguiEventDispatcher.updateProgress({ step: 'user_info' })
          console.log('🎯 Progress update result:', progressResult)
        } catch (error) {
          console.warn('🚨 Could not dispatch progress update:', error)
          // Continue - this is not a critical failure
        }
      }

      // Database persistence is handled by Mae's function tools

      return {
        success: true,
        message: `Successfully filled ${payload.field}`,
        data: { field: payload.field, value: payload.value }
      }

    } catch (error) {
      console.error('🚨 Error handling fillUserForm:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }, [addMaeMessage, currentStep])

  const handleSubmitUserForm = useCallback(async (payload: SubmitUserFormPayload, requestId: string): Promise<EventResponse> => {
    
    try {
      // Update UI first
      addMaeMessage('Mae: Great! I\'ve saving your profile information. Now let\'s add your family members.')
      setCurrentStep('family_info')

      // Database submission is handled by Mae's function tools

      // Trigger the main page step update via event dispatch
      try {
        const { aguiEventDispatcher } = await import('@/lib/ag-ui-events')
        const progressResult = await aguiEventDispatcher.updateProgress({ step: 'family_info' })
        console.log('🎯 Progress update result:', progressResult)
      } catch (error) {
        console.warn('🚨 Could not dispatch progress update:', error)
        // Continue - this is not a critical failure
      }

      // Update success message and trigger step change
      addMaeMessage('Mae: Perfect! Your profile has been saved. Now let\'s set up your family members!')
      
      // Ensure step change happens
      await notifyStepChange('family_info')
      
      // Also trigger completion check for onboarding flow
      if (familyMembers.length > 0) {
        setTimeout(() => {
          notifyStepChange('complete')
          setCurrentStep('complete')
          addMaeMessage('Mae: Excellent! Your onboarding is now complete!')
        }, 2000)
      }

      return {
        success: true,
        message: 'User form submitted successfully',
        data: { 
          step: 'family_info',
          completion: 'profile_submitted',
          next_step: 'family_members'
        }
      }

    } catch (error) {
      console.error('🚨 Error submitting user form:', error)
      addMaeMessage('Mae: I encountered an issue saving your profile. Let me try again.')
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to submit user profile'
      }
    }
  }, [addMaeMessage])

  const handleAddFamilyMember = useCallback(async (payload: AddFamilyMemberPayload, requestId: string): Promise<EventResponse> => {
    
    if (!payload.name || !payload.date_of_birth || !payload.relationship) {
      return {
        success: false,
        error: 'Name, date of birth, and relationship are required'
      }
    }

    try {
      // Update UI first
      const newMember: FamilyMemberData = {
        name: payload.name,
        date_of_birth: payload.date_of_birth,
        relationship: payload.relationship,
        gender: payload.gender,
        medical_conditions: payload.medical_conditions || [],
        allergies: payload.allergies || [],
        medications: payload.medications || [],
        additional_notes: payload.additional_notes
      }

      setFamilyMembers(prev => {
        const updated = [...prev, newMember]
        console.log('🎯 Updated family members in UI:', updated)
        return updated
      })
      
      addMaeMessage(`Mae: I'm adding ${payload.name} to your family as your ${payload.relationship}...`)

      // Ensure we're on family step
      if (currentStep !== 'family_info') {
        setCurrentStep('family_info')
        await notifyStepChange('family_info')
      }

      // Database persistence is handled by Mae's function tools
      
      // Update success message
      addMaeMessage(`Mae: Perfect! I've successfully added ${payload.name} to your family.`)

      return {
        success: true,
        message: `Successfully added family member ${payload.name}`,
        data: {
          ...newMember,
          database_id: null // Database persistence is handled by Mae's function tools
        }
      }

    } catch (error) {
      console.error('🚨 Error handling addFamilyMember:', error)
      addMaeMessage(`Mae: I had trouble saving ${payload.name} to your family. Let me try again.`)
      
      // Remove from UI since database failed
      setFamilyMembers(prev => prev.filter(member => member.name !== payload.name))
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: `Failed to add family member ${payload.name}`
      }
    }
  }, [addMaeMessage, currentStep])


  // Initialize component and notify parent of current step
  useEffect(() => {
    console.log('🎯 Mae Slate initializing on step:', currentStep)
    notifyStepChange(currentStep)
  }, [currentStep, notifyStepChange])

  // Register AG-UI event listeners
  useEffect(() => {
    addMaeMessage('Mae: Ready to help with your onboarding!')
    
    aguiEventListener.addEventListener(AG_UI_EVENTS.FILL_USER_FORM, handleFillUserForm)
    aguiEventListener.addEventListener(AG_UI_EVENTS.SUBMIT_USER_FORM, handleSubmitUserForm)
    aguiEventListener.addEventListener(AG_UI_EVENTS.ADD_FAMILY_MEMBER, handleAddFamilyMember)
    // Note: NOT listening to UPDATE_PROGRESS here - let the parent page handle it
    
    return () => {
      console.log('🎯 Cleaning up Mae slate AG-UI listeners')
      aguiEventListener.removeEventListener(AG_UI_EVENTS.FILL_USER_FORM)
      aguiEventListener.removeEventListener(AG_UI_EVENTS.SUBMIT_USER_FORM)
      aguiEventListener.removeEventListener(AG_UI_EVENTS.ADD_FAMILY_MEMBER)
    }
  }, [handleFillUserForm, handleSubmitUserForm, handleAddFamilyMember])

  const renderUserInfoSlate = () => (
    <Card className="border-2 border-dashed border-muted-foreground/30 dark:border-muted-foreground/50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-muted-foreground dark:text-muted-foreground">
          👤 Your Profile Information
          {Object.keys(userFields).length > 0 && (
            <Badge variant="secondary" className="ml-auto">
              {Object.keys(userFields).length} fields filled
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {Object.keys(userFields).length === 0 ? (
          <div className="text-center py-8 text-muted-foreground dark:text-muted-foreground/80">
            <div className="text-4xl mb-2">✨</div>
            <p>Tell Mae about yourself and I'll fill in your profile</p>
            <p className="text-sm mt-2">Try saying: "Mae, my name is John Smith"</p>
          </div>
        ) : (
          <div className="grid gap-3">
            {Object.entries(userFields).map(([field, value]) => (
              <div key={field} className="flex justify-between items-center p-3 rounded-lg bg-background dark:bg-muted/50 border border-border dark:border-muted">
                <span className="font-medium text-foreground dark:text-foreground capitalize">
                  {field.replace(/_/g, ' ')}:
                </span>
                <span className="text-muted-foreground dark:text-muted-foreground">
                  {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                </span>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )

  const renderFamilyInfoSlate = () => (
    <Card className="border-2 border-dashed border-muted-foreground/30 dark:border-muted-foreground/50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-muted-foreground dark:text-muted-foreground">
          👨‍👩‍👧‍👦 Family Members
          {familyMembers.length > 0 && (
            <Badge variant="secondary" className="ml-auto">
              {familyMembers.length} member{familyMembers.length !== 1 ? 's' : ''}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {familyMembers.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground dark:text-muted-foreground/80">
            <div className="text-4xl mb-2">👨‍👩‍👧‍👦</div>
            <p>Tell Mae about your family members</p>
            <p className="text-sm mt-2">Try saying: "Mae, add my daughter Emma, born March 15, 2018"</p>
          </div>
        ) : (
          <div className="space-y-3">
            {familyMembers.map((member, index) => (
              <div key={index} className="p-4 rounded-lg bg-background dark:bg-muted/50 border border-border dark:border-muted">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-semibold text-foreground dark:text-foreground">{member.name}</h4>
                    <p className="text-sm text-muted-foreground dark:text-muted-foreground">
                      {member.relationship} • Born: {new Date(member.date_of_birth).toLocaleDateString()}
                    </p>
                    {member.gender && (
                      <p className="text-xs text-muted-foreground dark:text-muted-foreground/80">
                        Gender: {member.gender}
                      </p>
                    )}
                  </div>
                </div>
                {(member.medical_conditions?.length || member.allergies?.length || member.additional_notes) && (
                  <div className="mt-3 pt-3 border-t border-border dark:border-muted text-xs">
                    {member.medical_conditions?.length && member.medical_conditions.length > 0 && (
                      <p className="text-muted-foreground dark:text-muted-foreground/80">
                        Medical: {member.medical_conditions.join(', ')}
                      </p>
                    )}
                    {member.allergies?.length && member.allergies.length > 0 && (
                      <p className="text-muted-foreground dark:text-muted-foreground/80">
                        Allergies: {member.allergies.join(', ')}
                      </p>
                    )}
                    {member.additional_notes && (
                      <p className="text-muted-foreground dark:text-muted-foreground/80">
                        Notes: {member.additional_notes}
                      </p>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )

  const renderCompleteSlate = () => (
    <Card className="border-2 border-green-200 dark:border-green-800">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-green-700 dark:text-green-400">
          ✅ Onboarding Complete
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-center py-8">
          <div className="text-6xl mb-4">🎉</div>
          <h3 className="text-xl font-semibold text-foreground dark:text-foreground mb-2">
            Welcome to Our Kidz!
          </h3>
          <p className="text-muted-foreground dark:text-muted-foreground">
            Mae is ready to provide personalized parenting guidance
          </p>
          <div className="mt-6 p-4 bg-green-50 dark:bg-green-950/50 rounded-lg">
            <div className="text-sm text-green-800 dark:text-green-200 space-y-1">
              <p>✅ Profile: {Object.keys(userFields).length} fields completed</p>
              <p>✅ Family: {familyMembers.length} member{familyMembers.length !== 1 ? 's' : ''} added</p>
              <p>✅ Ready for AI assistance</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  const getStepContent = () => {
    switch (currentStep) {
      case 'welcome':
        return (
          <div className="text-center py-12">
            <div className="text-8xl mb-6">✨</div>
            <h2 className="text-2xl font-bold text-foreground dark:text-foreground mb-4">
              Welcome to Our Kidz
            </h2>
            <p className="text-lg text-muted-foreground dark:text-muted-foreground mb-6">
              Mae is here to help set up your personalized profile
            </p>
            <div className="max-w-md mx-auto p-4 bg-muted/50 dark:bg-muted/20 rounded-lg">
              <p className="text-sm text-muted-foreground dark:text-muted-foreground">
                💡 <strong>Just talk to Mae!</strong> She'll guide you through the setup and fill everything out for you.
              </p>
            </div>
          </div>
        )
      case 'user_info':
        return renderUserInfoSlate()
      case 'family_info':
        return renderFamilyInfoSlate()
      case 'complete':
        return renderCompleteSlate()
      default:
        return null
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Mae's Status Header */}
      <div className="p-4 bg-gradient-to-r from-blue-50 to-teal-50 dark:from-blue-950/20 dark:to-teal-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-2xl">✨</span>
            <div>
              <h2 className="font-semibold text-blue-900 dark:text-blue-100">Onboarding with Mae</h2>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Current Step: {currentStep === 'welcome' ? 'Getting Started' : 
                             currentStep === 'user_info' ? 'Your Profile' : 
                             currentStep === 'family_info' ? 'Family Members' : 'Complete!'}
              </p>
            </div>
          </div>
          <div className="flex gap-1 items-center">
            {(['welcome', 'user_info', 'family_info', 'complete'] as OnboardingStep[]).map((step) => (
              <div
                key={step}
                className={`w-2 h-2 rounded-full ${
                  step === currentStep 
                    ? 'bg-blue-600 dark:bg-blue-400' 
                    : 'bg-blue-200 dark:bg-blue-800'
                }`}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Dynamic Content Based on Step */}
      {getStepContent()}

    </div>
  )
}