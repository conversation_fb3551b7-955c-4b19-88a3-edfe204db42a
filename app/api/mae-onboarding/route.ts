import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { 
  getCompleteUserProfile, 
  updateCompleteUserProfile, 
  getOnboardingProgress, 
  generateOnboardingUI,
  type CompleteUserProfile 
} from '@/lib/mae-complete-profile-tools'

/**
 * Mae Onboarding API Route
 * 
 * Provides <PERSON> with comprehensive user profile management and AG-UI integration
 * for dynamic onboarding UI generation
 */

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'profile':
        const profile = await getCompleteUserProfile(userId)
        return NextResponse.json({ success: true, profile })

      case 'progress':
        const progress = await getOnboardingProgress(userId)
        return NextResponse.json({ success: true, progress })

      case 'ui-instructions':
        const uiInstructions = await generateOnboardingUI(userId)
        return NextResponse.json({ success: true, uiInstructions })

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

  } catch (error) {
    console.error('🚨 Mae Onboarding API Error:', error)
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action, data } = body

    switch (action) {
      case 'update-profile':
        const updatedProfile = await updateCompleteUserProfile(data as Partial<CompleteUserProfile>)
        return NextResponse.json({ success: true, profile: updatedProfile })

      case 'trigger-ui-event':
        // This will be used by Mae to trigger AG-UI events for dynamic UI generation
        const { eventType, payload } = data
        
        // Log the UI event trigger for Mae's context
        console.log(`🎯 Mae triggered UI event: ${eventType}`, payload)
        
        return NextResponse.json({ 
          success: true, 
          message: `UI event ${eventType} triggered successfully`,
          eventType,
          payload 
        })

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

  } catch (error) {
    console.error('🚨 Mae Onboarding API Error:', error)
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
}