import { NextRequest, NextResponse } from 'next/server'
import { getOnboardingService, type User, supabase } from '@/lib/supabase-client'
import { createClient } from '@supabase/supabase-js'
import { auth, currentUser } from '@clerk/nextjs/server'

// Rate limiting store
const rateLimitStore = new Map<string, { count: number; lastRequest: number }>()
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 50 // 50 requests per minute per IP (increased for onboarding flow)

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = rateLimitStore.get(clientIP)

  if (!clientData) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  if (now - clientData.lastRequest > RATE_LIMIT_WINDOW) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  clientData.count++
  clientData.lastRequest = now
  return true
}

function getClientIP(request: NextRequest): string {
  return request.ip || 
    (request.headers.get('x-forwarded-for')?.split(',')[0] as string) || 
    request.headers.get('x-real-ip') || 
    'unknown'
}

// Helper function to get authenticated user using Clerk
async function getAuthenticatedUser() {
  const user = await currentUser()
  
  if (!user) {
    console.log('No authenticated user found')
    return null
  }
  
  console.log('Auth verified for Clerk user:', user.emailAddresses[0]?.emailAddress)
  return {
    id: user.id,
    email: user.emailAddresses[0]?.emailAddress || '',
    user_metadata: {
      full_name: user.fullName || user.firstName || ''
    }
  }
}

// POST: Create a new user
export async function POST(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const rawUserData: Partial<User> = await request.json()
    
    // Validate required fields
    if (!rawUserData.email || !rawUserData.name) {
      return NextResponse.json(
        { error: 'Email and name are required' },
        { status: 400 }
      )
    }

    // Clean up the user data - convert empty strings to null for date fields
    const userData: Partial<User> = {
      ...rawUserData,
      date_of_birth: rawUserData.date_of_birth === '' ? undefined : rawUserData.date_of_birth
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(userData.email!)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Try to get authenticated user using Clerk
    const authUser = await getAuthenticatedUser()
    console.log('Clerk user for creation:', authUser?.email || 'No auth user')

    // Check if user already exists
    const { data: existingUser } = await getOnboardingService().getUserByEmail(userData.email!)
    if (existingUser) {
      // If user exists, update it instead of creating new one
      // Update clerk_user_id if we have one
      const shouldUpdateClerkId = authUser?.id && (existingUser.clerk_id !== authUser.id || !existingUser.clerk_id)
      console.log('Should update clerk id:', shouldUpdateClerkId)
      const { data: updatedUser, error: updateError } = await getOnboardingService().updateUser(existingUser.id, {
        ...userData,
        clerk_id: shouldUpdateClerkId ? authUser.id : (existingUser.clerk_id ? existingUser.clerk_id : undefined),
        auth_user_id: existingUser.auth_user_id ? existingUser.auth_user_id : undefined, // Keep existing Supabase auth ID if any
        onboarding_completed: false,
        onboarding_step: 'user_info'
      })
      console.log('Updated user:', updatedUser)
      if (updateError) {
        console.error('Error updating existing user:', updateError)
        console.error('Update error details:', {
          message: updateError.message,
          code: updateError.code,
          details: updateError.details,
          hint: updateError.hint
        })
        console.error('User data that failed to update:', userData)
        return NextResponse.json(
          { error: 'Failed to update user', details: updateError.message },
          { status: 500 }
        )
      }
      console.log('Updated user:', updatedUser)
      console.log('✅ User updated successfully:', updatedUser?.email)
      
      return NextResponse.json({
        success: true,
        message: 'User updated successfully',
        user: updatedUser
      })
    }

    // Create new user
    const { data: newUser, error } = await getOnboardingService().createUser({
      ...userData,
      clerk_id: authUser?.id ? authUser.id : undefined,
      auth_user_id: authUser?.id ? authUser.id : undefined, // No longer using Supabase auth
      onboarding_completed: false,
      onboarding_step: 'user_info'
    })
    console.log('New user:', newUser)
    if (error) {
      console.error('Error creating user:', error)
      console.error('Error details:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      })
      console.error('User data that failed:', userData)
      return NextResponse.json(
        { error: 'Failed to create user', details: error.message },
        { status: 500 }
      )
    }
    console.log('New user:', newUser)
    console.log('✅ User created successfully:', newUser?.email)
    
    return NextResponse.json({
      success: true,
      message: 'User created successfully',
      user: newUser
    })
    console.log('New user:', newUser)
  } catch (error) {
    console.error('Error in POST /api/onboarding/users:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET: Get user by email
export async function GET(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')

    if (!email) {
      return NextResponse.json(
        { error: 'Email parameter is required' },
        { status: 400 }
      )
    }

    console.log('Looking up user by email:', email)

    const { data: user, error } = await getOnboardingService().getUserByEmail(email)
    console.log('User:', user)    
    if (error) {
      console.error('Error fetching user:', error)
      
      // If it's a "no rows" error, that means user doesn't exist yet - return 404 instead of 500
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'User not found', user: null },
          { status: 404 }
        )
      }
      
      return NextResponse.json(
        { error: 'Failed to fetch user' },
        { status: 500 }
      )
    }

    if (!user) {
      return NextResponse.json(
        { error: 'User not found', user: null },
        { status: 404 }
      )
    }

    console.log('User found:', user.email)

    return NextResponse.json({
      success: true,
      user: user
    })

  } catch (error) {
    console.error('Error in GET /api/onboarding/users:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT: Update user
export async function PUT(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const rawUpdateData = await request.json()
    
    if (!rawUpdateData.id && !rawUpdateData.email) {
      return NextResponse.json(
        { error: 'User ID or email is required' },
        { status: 400 }
      )
    }

    let userId = rawUpdateData.id
    
    // If no ID provided, get user by email
    if (!userId && rawUpdateData.email) {
      const { data: user, error } = await getOnboardingService().getUserByEmail(rawUpdateData.email)
      if (error || !user) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        )
      }
      userId = user.id
    }

    // Remove id from update data and clean up date fields
    const { id, ...rawUserData } = rawUpdateData
    const userData = {
      ...rawUserData,
      date_of_birth: rawUserData.date_of_birth === '' ? null : rawUserData.date_of_birth
    }

    const { data: updatedUser, error } = await getOnboardingService().updateUser(userId, userData)

    if (error) {
      console.error('Error updating user:', error)
      return NextResponse.json(
        { error: 'Failed to update user' },
        { status: 500 }
      )
    }

    console.log('✅ User updated successfully:', updatedUser?.email)
    
    return NextResponse.json({
      success: true,
      message: 'User updated successfully',
      user: updatedUser
    })

  } catch (error) {
    console.error('Error in PUT /api/onboarding/users:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}