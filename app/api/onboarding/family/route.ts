import { NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import { createClient } from "@supabase/supabase-js";

export async function POST(request: Request) {
  const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!);
  const { userId } = await auth();

  if (!userId) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  const { name, dateOfBirth, relationship } = await request.json();

  if (!name || !dateOfBirth || !relationship) {
    return new NextResponse("Missing required fields", { status: 400 });
  }

  try {
    const { data, error } = await supabase
      .from("family_members")
      .insert([
        {
          user_id: userId,
          name: name,
          date_of_birth: dateOfBirth,
          relationship: relationship,
        },
      ])
      .select();

    if (error) {
      console.error("Supabase error:", error);
      return new NextResponse("Internal Server Error", { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error("Error adding family member:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
