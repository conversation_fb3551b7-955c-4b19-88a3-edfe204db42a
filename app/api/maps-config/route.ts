import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Get the API key from server-side environment variables
    const apiKey = process.env.GOOGLE_MAPS_API_KEY
    
    if (!apiKey) {
      return NextResponse.json(
        { error: 'Google Maps API key not configured' },
        { status: 500 }
      )
    }

    // For Maps, we need to provide the key to the client, but we can add restrictions
    // and validate the request origin
    const origin = request.headers.get('origin')
    const referer = request.headers.get('referer')
    
    // Basic origin validation (add your domain restrictions here)
    const allowedOrigins = [
      'http://localhost:3000',
      'https://localhost:3000',
      process.env.NEXT_PUBLIC_APP_URL,
      // Add your production domains here
    ].filter(Boolean)

    if (origin && !allowedOrigins.some(allowed => origin.startsWith(allowed || ''))) {
      return NextResponse.json(
        { error: 'Origin not allowed' },
        { status: 403 }
      )
    }

    return NextResponse.json({
      success: true,
      apiKey: apiKey, // Maps API requires client-side key, but we validate the request
    })

  } catch (error) {
    console.error('Error in maps-config:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}