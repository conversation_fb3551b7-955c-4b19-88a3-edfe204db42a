import { NextRequest, NextResponse } from 'next/server'

// Rate limiting store
const rateLimitStore = new Map<string, { count: number; lastRequest: number }>()
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 10 // 10 requests per minute per IP

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = rateLimitStore.get(clientIP)

  if (!clientData) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  if (now - clientData.lastRequest > RATE_LIMIT_WINDOW) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  clientData.count++
  clientData.lastRequest = now
  return true
}

function getClientIP(request: NextRequest): string {
  return request.ip || 
    request.headers.get('x-forwarded-for')?.split(',')[0] || 
    request.headers.get('x-real-ip') || 
    'unknown'
}

export async function POST(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    // Get the API key from server-side environment variables
    const apiKey = process.env.GEMINI_API_KEY
    
    if (!apiKey) {
      return NextResponse.json(
        { error: 'Gemini API key not configured' },
        { status: 500 }
      )
    }

    // Validate request origin
    const origin = request.headers.get('origin')
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'https://localhost:3000',
      'https://localhost:3001',
      process.env.NEXT_PUBLIC_APP_URL,
      'https://our-kidz.com',
      'https://www.our-kidz.com',
      'https://app.our-kidz.com'
    ].filter(Boolean)

    if (origin && !allowedOrigins.includes(origin)) {
      return NextResponse.json(
        { error: 'Origin not allowed' },
        { status: 403 }
      )
    }

    // Generate ephemeral token for enhanced security
    const ephemeralToken = Buffer.from(
      JSON.stringify({
        timestamp: Date.now(),
        ip: clientIP,
        nonce: Math.random().toString(36).substr(2, 16),
        expires: Date.now() + (60 * 60 * 1000) // 1 hour
      })
    ).toString('base64')

    return NextResponse.json({
      success: true,
      ephemeralToken,
      // For Gemini Live, we unfortunately need to provide the API key to the client
      // But we can add additional validation and monitoring
      apiKey: apiKey,
      expiresAt: Date.now() + (60 * 60 * 1000) // 1 hour
    })

  } catch (error) {
    console.error('Error in gemini-proxy:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}