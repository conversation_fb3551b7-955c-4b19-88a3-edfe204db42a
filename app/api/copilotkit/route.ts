import { CopilotRuntime, GoogleGenerativeAIAdapter, copilotRuntimeNextJSAppRouterEndpoint } from '@copilotkit/runtime';
import {
  handleFindPediatricians,
  handleFindHospitals,
  handleFindUrgentCare,
  handleFindPharmacies
} from '@/lib/location-function-tools';
// <PERSON>'s AG-UI function tools need to be handled differently due to server-side restrictions

const runtime = new CopilotRuntime({
  actions: [
        {
          name: 'find_pediatricians',
          description: 'Find nearby pediatricians based on user location, insurance, and preferences.',
          parameters: [
            {
              name: 'location',
              type: 'string',
              description: 'User location (address, city, or zip code)',
              required: true
            },
            {
              name: 'radius',
              type: 'number',
              description: 'Search radius in miles (default: 10)'
            },
            {
              name: 'insurance',
              type: 'string',
              description: 'Insurance provider name (optional)'
            },
            {
              name: 'specialization',
              type: 'string',
              description: 'Pediatric specialization if needed (e.g., cardiology, neurology)'
            },
            {
              name: 'rating_threshold',
              type: 'number',
              description: 'Minimum rating threshold (1-5 stars)'
            }
          ],
          handler: async (args: any) => {
            console.log('🔍 CopilotKit: Finding pediatricians', args);
            const { location, radius, insurance, specialization, rating_threshold } = args;
            const result = await handleFindPediatricians({
              location,
              radius,
              insurance,
              specialization,
              rating_threshold
            });

            // Note: Map display is handled client-side via the Gemini Live hook
            // Server-side API routes cannot access window object

            return result;
          }
        },
        {
          name: 'find_hospitals',
          description: 'Find nearby hospitals with pediatric departments and emergency services.',
          parameters: [
            {
              name: 'location',
              type: 'string',
              description: 'User location (address, city, or zip code)',
              required: true
            },
            {
              name: 'radius',
              type: 'number',
              description: 'Search radius in miles (default: 25)'
            },
            {
              name: 'emergency_only',
              type: 'boolean',
              description: 'Filter for hospitals with 24/7 emergency services'
            },
            {
              name: 'pediatric_level',
              type: 'string',
              description: 'Required pediatric care level (basic, intermediate, advanced)'
            },
            {
              name: 'insurance',
              type: 'string',
              description: 'Insurance provider name (optional)'
            }
          ],
          handler: async (args: any) => {
            console.log('🏥 CopilotKit: Finding hospitals', args);
            const { location, radius, emergency_only, pediatric_level, insurance } = args;
            const result = await handleFindHospitals({
              location,
              radius,
              emergency_only,
              pediatric_level,
              insurance
            });

            // Note: Map display is handled client-side via the Gemini Live hook

            return result;
          }
        },
        {
          name: 'find_urgent_care',
          description: 'Find nearby urgent care centers that accept pediatric patients.',
          parameters: [
            {
              name: 'location',
              type: 'string',
              description: 'User location (address, city, or zip code)',
              required: true
            },
            {
              name: 'radius',
              type: 'number',
              description: 'Search radius in miles (default: 15)'
            },
            {
              name: 'accepts_children',
              type: 'boolean',
              description: 'Filter for centers that specifically accept pediatric patients'
            },
            {
              name: 'current_hours',
              type: 'boolean',
              description: 'Filter for centers currently open'
            },
            {
              name: 'insurance',
              type: 'string',
              description: 'Insurance provider name (optional)'
            }
          ],
          handler: async (args: any) => {
            console.log('🚑 CopilotKit: Finding urgent care', args);
            const { location, radius, accepts_children, current_hours, insurance } = args;
            const result = await handleFindUrgentCare({
              location,
              radius,
              accepts_children,
              current_hours,
              insurance
            });

            // Note: Map display is handled client-side via the Gemini Live hook

            return result;
          }
        },
        {
          name: 'find_pharmacies',
          description: 'Find nearby pharmacies that stock pediatric medications and offer services for children.',
          parameters: [
            {
              name: 'location',
              type: 'string',
              description: 'User location (address, city, or zip code)',
              required: true
            },
            {
              name: 'radius',
              type: 'number',
              description: 'Search radius in miles (default: 10)'
            },
            {
              name: 'medication_type',
              type: 'string',
              description: 'Specific medication or type needed (optional)'
            },
            {
              name: 'services',
              type: 'string',
              description: 'Required services (immunizations, compounding, delivery)'
            },
            {
              name: 'insurance',
              type: 'string',
              description: 'Insurance/prescription plan name (optional)'
            },
            {
              name: 'open_now',
              type: 'boolean',
              description: 'Filter for pharmacies currently open'
            }
          ],
          handler: async (args: any) => {
            console.log('💊 CopilotKit: Finding pharmacies', args);
            const { location, radius, medication_type, services, insurance, open_now } = args;
            const result = await handleFindPharmacies({
              location,
              radius,
              medication_type,
              services: services ? [services] : [],
              insurance,
              open_now
            });

            // Note: Map display is handled client-side via the Gemini Live hook

            return result;
          }
        },
        // Mae's AG-UI functions are now handled directly in the React components
        // via useCopilotAction hooks for proper client-side integration
      ]
});

export const POST = copilotRuntimeNextJSAppRouterEndpoint({
  runtime,
  serviceAdapter: new GoogleGenerativeAIAdapter({
    model: 'gemini-2.5-flash' // if you change this, I will murder 10 kittens.
  }),
  endpoint: '/api/copilotkit',
});