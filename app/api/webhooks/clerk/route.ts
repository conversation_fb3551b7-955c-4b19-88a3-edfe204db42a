import { headers } from 'next/headers'
import { Webhook } from 'svix'
import { WebhookEvent } from '@clerk/nextjs/server'
import { syncUserWithSupabase } from '@/lib/user-sync-helper'
import { createServiceRoleClient } from '@/lib/clerk-supabase-server'

export async function POST(req: Request) {
  console.log('🔔 Clerk webhook received')
  
  // Get the headers
  const headerPayload = await headers()
  const svix_id = headerPayload.get("svix-id")
  const svix_timestamp = headerPayload.get("svix-timestamp")
  const svix_signature = headerPayload.get("svix-signature")

  console.log('📋 Headers:', { svix_id: !!svix_id, svix_timestamp: !!svix_timestamp, svix_signature: !!svix_signature })

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    console.error('❌ Missing svix headers')
    return new Response('Error occured -- no svix headers', {
      status: 400
    })
  }

  // Get the body
  const payload = await req.json()
  const body = JSON.stringify(payload)

  console.log('📦 Payload received, event type:', payload.type)

  // Check if webhook secret is configured
  if (!process.env.CLERK_WEBHOOK_SECRET) {
    console.error('❌ CLERK_WEBHOOK_SECRET not configured')
    return new Response('Webhook secret not configured', {
      status: 500
    })
  }

  // Create a new Svix instance with your webhook secret
  const wh = new Webhook(process.env.CLERK_WEBHOOK_SECRET)

  let evt: WebhookEvent

  // Verify the webhook signature
  try {
    evt = wh.verify(body, {
      "svix-id": svix_id,
      "svix-timestamp": svix_timestamp,
      "svix-signature": svix_signature,
    }) as WebhookEvent
    console.log('✅ Webhook signature verified')
  } catch (err) {
    console.error('❌ Error verifying webhook:', err)
    return new Response('Error occured', {
      status: 400
    })
  }

  // Handle the webhook events
  const eventType = evt.type
  console.log('🎯 Processing event:', eventType)

  if (eventType === 'user.created' || eventType === 'user.updated') {
    const { id, email_addresses, first_name, last_name, phone_numbers, image_url } = evt.data

    // Get primary email
    const primaryEmail = email_addresses.find((email) => email.id === evt.data.primary_email_address_id)?.email_address
    
    if (!primaryEmail) {
      console.error('No primary email found for user:', id)
      return new Response('No primary email found', { status: 400 })
    }

    // Get primary phone
    const primaryPhone = phone_numbers && phone_numbers.length > 0 
      ? phone_numbers.find((phone) => phone.id === evt.data.primary_phone_number_id)?.phone_number || phone_numbers[0]?.phone_number
      : null

    try {
      // Use the new sync helper
      const result = await syncUserWithSupabase({
        clerkId: id,
        email: primaryEmail,
        firstName: first_name || '',
        lastName: last_name || '',
        fullName: `${first_name || ''} ${last_name || ''}`.trim() || 'User',
        phone: primaryPhone || '',
        imageUrl: image_url
      })

      console.log(`✅ Webhook sync result: ${result.status} - ${result.message}`)
      
    } catch (error) {
      console.error('Error syncing user to Supabase:', error)
      return new Response('Error syncing user', { status: 500 })
    }
  }

  if (eventType === 'user.deleted') {
    const { id } = evt.data

    try {
      // Get Supabase admin client
      const supabaseAdmin = createServiceRoleClient()
      
      // Delete user from Supabase (cascade will handle related records)
      const { error: deleteError } = await supabaseAdmin
        .from('users')
        .delete()
        .eq('clerk_id', id)

      if (deleteError) {
        console.error('Error deleting user:', deleteError)
        throw deleteError
      }
      console.log(`Deleted user with clerk_id: ${id}`)
    } catch (error) {
      console.error('Error deleting user from Supabase:', error)
      return new Response('Error deleting user', { status: 500 })
    }
  }

  return new Response('Webhook processed successfully', { status: 200 })
}