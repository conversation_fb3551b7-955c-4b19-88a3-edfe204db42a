import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import {
  fillUserProfileField,
  submitUserProfileForm,
  addFamilyMemberWithUI,
  guideUserThroughOnboarding,
  getOnboardingStatus,
  generatePersonalizedOnboardingMessage
} from '@/lib/mae-agui-function-tools'

/**
 * Mae AG-UI Tools API Route
 * 
 * This route provides <PERSON> with access to AG-UI integrated functions
 * for dynamic onboarding and profile management
 */

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action, args } = body

    // Ensure userId is included in args
    const argsWithUserId = { ...args, userId }

    console.log(`🎯 Mae AG-UI Tools API: ${action}`, argsWithUserId)

    switch (action) {
      case 'fill_user_field':
        const fillResult = await fillUserProfileField(argsWithUserId)
        return NextResponse.json(fillResult)

      case 'submit_user_profile':
        const submitResult = await submitUserProfileForm(argsWithUserId)
        return NextResponse.json(submitResult)

      case 'add_family_member':
        const addResult = await addFamilyMemberWithUI(argsWithUserId)
        return NextResponse.json(addResult)

      case 'guide_onboarding':
        const guideResult = await guideUserThroughOnboarding(argsWithUserId)
        return NextResponse.json(guideResult)

      case 'get_status':
        const statusResult = await getOnboardingStatus(argsWithUserId)
        return NextResponse.json(statusResult)

      case 'generate_message':
        const messageResult = await generatePersonalizedOnboardingMessage(argsWithUserId)
        return NextResponse.json(messageResult)

      default:
        return NextResponse.json({ 
          success: false,
          error: 'Invalid action',
          available_actions: [
            'fill_user_field',
            'submit_user_profile', 
            'add_family_member',
            'guide_onboarding',
            'get_status',
            'generate_message'
          ]
        }, { status: 400 })
    }

  } catch (error) {
    console.error('🚨 Mae AG-UI Tools API Error:', error)
    return NextResponse.json({ 
      success: false,
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Default GET behavior: return onboarding status
    const statusResult = await getOnboardingStatus({ clerkId: userId })
    return NextResponse.json(statusResult)

  } catch (error) {
    console.error('🚨 Mae AG-UI Tools API Error:', error)
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
}