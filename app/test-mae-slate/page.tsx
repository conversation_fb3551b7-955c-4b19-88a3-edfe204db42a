"use client"

import React, { useState } from 'react'
import { useUser } from '@clerk/nextjs'
import { Button } from '@/components/ui/button'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { aguiEventDispatcher } from '@/lib/ag-ui-events'

export default function TestMaeSlatePage() {
  const { user, isLoaded } = useUser()
  const [responses, setResponses] = useState<string[]>([])
  const [loading, setLoading] = useState(false)

  if (!isLoaded || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p>Please sign in to test <PERSON>'s slate functionality</p>
          <Button onClick={() => window.location.href = '/auth'}>Sign In</Button>
        </div>
      </div>
    )
  }

  const addResponse = (message: string) => {
    setResponses(prev => [message, ...prev])
  }

  const testFillName = async () => {
    setLoading(true)
    addResponse("Testing: Mae fills name field...")
    
    try {
      const response = await aguiEventDispatcher.fillUserForm({
        field: 'name',
        value: '<PERSON>',
        validate: true
      })
      addResponse(`Mae: ${response.success ? response.message : response.error}`)
    } catch (error) {
      addResponse(`Error: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  const testFillEmail = async () => {
    setLoading(true)
    addResponse("Testing: Mae fills email field...")
    
    try {
      const response = await aguiEventDispatcher.fillUserForm({
        field: 'email',
        value: user.emailAddresses[0]?.emailAddress || '<EMAIL>',
        validate: true
      })
      addResponse(`Mae: ${response.success ? response.message : response.error}`)
    } catch (error) {
      addResponse(`Error: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  const testFillPhone = async () => {
    setLoading(true)
    addResponse("Testing: Mae fills phone field...")
    
    try {
      const response = await aguiEventDispatcher.fillUserForm({
        field: 'phone',
        value: '(*************',
        validate: true
      })
      addResponse(`Mae: ${response.success ? response.message : response.error}`)
    } catch (error) {
      addResponse(`Error: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  const testSubmitProfile = async () => {
    setLoading(true)
    addResponse("Testing: Mae submits profile...")
    
    try {
      const response = await aguiEventDispatcher.submitUserForm({
        validate: false
      })
      addResponse(`Mae: ${response.success ? response.message : response.error}`)
    } catch (error) {
      addResponse(`Error: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  const testAddFamilyMember = async () => {
    setLoading(true)
    addResponse("Testing: Mae adds family member...")
    
    try {
      const response = await aguiEventDispatcher.addFamilyMember({
        name: 'Emma Smith',
        date_of_birth: '2018-03-15',
        relationship: 'child',
        gender: 'female',
        medical_conditions: ['None'],
        allergies: ['Peanuts'],
        additional_notes: 'Loves drawing and soccer'
      })
      addResponse(`Mae: ${response.success ? response.message : response.error}`)
    } catch (error) {
      addResponse(`Error: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  const testUpdateProgress = async () => {
    setLoading(true)
    addResponse("Testing: Mae updates progress to complete...")
    
    try {
      const response = await aguiEventDispatcher.updateProgress({
        step: 'complete',
        data: { completed_at: new Date().toISOString() }
      })
      addResponse(`Mae: ${response.success ? response.message : response.error}`)
    } catch (error) {
      addResponse(`Error: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen p-6 bg-gradient-to-br from-blue-50 to-teal-50 dark:from-blue-950/20 dark:to-teal-950/20">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-2 mb-4">
            <span className="text-3xl">🎯</span>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Mae Slate Testing</h1>
          </div>
          <p className="text-gray-600 dark:text-gray-300">
            Test Mae's ability to fill the clean slate onboarding interface via AG-UI events
          </p>
          <p className="text-sm text-muted-foreground dark:text-muted-foreground mt-2">
            Open `/onboarding` in another tab to see the slate update in real-time
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 mb-8">
          <Card className="dark:bg-card dark:border-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 dark:text-card-foreground">
                👤 Profile Fields
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                onClick={testFillName} 
                disabled={loading}
                className="w-full"
                variant="outline"
              >
                Fill Name: "John Smith"
              </Button>
              <Button 
                onClick={testFillEmail} 
                disabled={loading}
                className="w-full"
                variant="outline"
              >
                Fill Email: Your Clerk Email
              </Button>
              <Button 
                onClick={testFillPhone} 
                disabled={loading}
                className="w-full"
                variant="outline"
              >
                Fill Phone: "(*************"
              </Button>
              <Button 
                onClick={testSubmitProfile} 
                disabled={loading}
                className="w-full"
                variant="default"
              >
                Submit Profile
              </Button>
            </CardContent>
          </Card>

          <Card className="dark:bg-card dark:border-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 dark:text-card-foreground">
                👨‍👩‍👧‍👦 Family & Progress
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                onClick={testAddFamilyMember} 
                disabled={loading}
                className="w-full"
                variant="outline"
              >
                Add Emma (daughter)
              </Button>
              <Button 
                onClick={testUpdateProgress} 
                disabled={loading}
                className="w-full"
                variant="default"
              >
                Mark Complete
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Response Log */}
        <Card className="dark:bg-card dark:border-border">
          <CardHeader>
            <CardTitle className="dark:text-card-foreground">AG-UI Event Responses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {responses.length === 0 ? (
                <p className="text-gray-500 dark:text-gray-400 italic">
                  No responses yet. Click a button above to test Mae's AG-UI integration!
                </p>
              ) : (
                responses.map((response, index) => (
                  <div 
                    key={index} 
                    className="p-3 bg-gray-50 dark:bg-muted/50 rounded-lg border-l-4 border-blue-500"
                  >
                    <p className="text-sm text-gray-700 dark:text-gray-300">{response}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                      {new Date().toLocaleTimeString()}
                    </p>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <div className="mt-8 p-6 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">How to Test</h3>
          <div className="text-sm text-blue-700 dark:text-blue-300 space-y-2">
            <p>• Open `/onboarding` in another tab to see the clean slate interface</p>
            <p>• Click buttons here to trigger Mae's AG-UI events</p>
            <p>• Watch the onboarding page update in real-time as Mae fills fields</p>
            <p>• The UI will change steps automatically as Mae progresses</p>
            <p>• <strong>Dark mode colors are properly implemented</strong></p>
          </div>
        </div>
      </div>
    </div>
  )
}