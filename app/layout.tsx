import type React from "react"
import "./globals.css"
import { <PERSON><PERSON><PERSON> } from "next/font/google"
import { ThemeProvider } from "next-themes"
import { Toaster } from "@/components/ui/toaster"
import { Toaster as Sonner } from "@/components/ui/sonner"
import CopilotProvider from "@/components/CopilotProvider"
import { MaeControlledMap } from "@/components/MaeControlledMap"
import { GlobalAGUIHandler } from "@/components/global-agui-handler"
import { GlobalNavigationHandler } from "@/components/global-navigation-handler"
import { GlobalFAB } from "@/components/global-fab"
import { GlobalMaeProvider } from "@/components/global-mae-provider"

import { type Metadata } from 'next'
import {
  ClerkProvider,
} from '@clerk/nextjs'

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-poppins",
})

export const metadata: Metadata = {
  metadataBase: new URL("https://app.our-kidz.com"),
  icons: {
    icon: "/OKdarkTsp.png",
  },
  title: "our kidz - AI-Powered Parenting Platform",
  description: "Revolutionizing parenting with AI-powered insights.",
    generator: 'v0.1.0'
}


export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <ClerkProvider>
      <html lang="en" suppressHydrationWarning>
        <head>
          <script src="https://cdn.jsdelivr.net/npm/mermaid@10.9.0/dist/mermaid.min.js"></script>
          <script dangerouslySetInnerHTML={{
            __html: `
              if (typeof window !== 'undefined') {
                window.addEventListener('load', function() {
                  if (typeof mermaid !== 'undefined') {
                    mermaid.initialize({ 
                      startOnLoad: true,
                      theme: 'neutral',
                      flowchart: {
                        curve: 'basis',
                        padding: 10
                      }
                    });
                  }
                });
              }
            `
          }} />
        </head>
        <body className={poppins.className}>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
            storageKey="our-kidz-theme"
          >
            <CopilotProvider>
              <GlobalMaeProvider>
                <GlobalAGUIHandler />
                <GlobalNavigationHandler />
                {children}
                <Toaster />
                <Sonner />
                <MaeControlledMap />
                <GlobalFAB />
              </GlobalMaeProvider>
            </CopilotProvider>
          </ThemeProvider>
        </body>
      </html>
    </ClerkProvider>
  )
}

// export default function RootLayout({
//   children,
// }: {
//   children: React.ReactNode
// }) {
//   return (
//     <html lang="en" suppressHydrationWarning>
//       <body className={poppins.className}>
//         <ThemeProvider
//           attribute="class"
//           defaultTheme="system"
//           enableSystem
//           disableTransitionOnChange
//           storageKey="our-kidz-theme"
//         >
//           <AuthProvider>
//             <CopilotProvider>
//               <GlobalAGUIHandler />
//               <GlobalNavigationHandler />
//               {children}
//               <Toaster />
//               <MaeControlledMap />
//               <MaeAuthModal />
//               <GlobalFAB />
//             </CopilotProvider>
//           </AuthProvider>
//         </ThemeProvider>
//       </body>
//     </html>
//   )
// }
