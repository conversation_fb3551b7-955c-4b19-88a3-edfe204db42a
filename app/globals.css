@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 174 100% 37%;
    --primary-foreground: 0 0% 98%;
    --secondary: 210 40% 98%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 98%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 98%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 174 100% 37%;
    --radius: 0.75rem !important;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 174 100% 37%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 174 100% 37%;
    --radius: 0.75rem !important;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-weight: 300; /* Make body text thinner */
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: 400; /* Make headings thinner */
    letter-spacing: -0.025em;
    line-height: 1.2;
  }

  /* Override common font weights to be thinner */
  .font-semibold {
    font-weight: 400 !important;
  }

  .font-bold {
    font-weight: 500 !important;
  }

  html {
    scroll-behavior: smooth;
  }
  .theme {
    --animate-border-beam: border-beam calc(var(--duration)*1s) infinite linear;
  }
}

/* @layer utilities {
  .text-balance {
    text-wrap: balance;
  } */
  
  /* Force border radius on all UI components */
  /* .rounded-md,
  [class*="rounded-md"] {
    border-radius: calc(var(--radius) - 2px) !important;
  }
  
  .rounded-lg,
  [class*="rounded-lg"] {
    border-radius: var(--radius) !important;
  }
  
  .rounded-sm,
  [class*="rounded-sm"] {
    border-radius: calc(var(--radius) - 4px) !important;
  } */
  
  /* Additional overrides for common button and card classes */
  /* button,
  .btn,
  [role="button"] {
    border-radius: calc(var(--radius) - 2px) !important;
  } */
  
  /* .card,
  [data-radix-card] {
    border-radius: var(--radius) !important;
  } */
/* } */

/* Enhanced Clerk Integration for Better Dark Mode Contrast */
.clerk-wrapper {
  width: 100%;
}

/* Premium Dark Mode Design */
.dark .clerk-wrapper .cl-card {
  background: hsl(222, 28%, 12%) !important;
  border: 1px solid hsl(210, 93%, 16%) !important;
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.15),
    0 0 0 1px hsl(210 50% 15%),
    inset 0 1px 0 hsl(210 50% 25%) !important;
}

.dark .clerk-wrapper .cl-formFieldInput,
.dark .clerk-wrapper .cl-selectButton__countryCode,
.dark .clerk-wrapper .cl-phoneNumberInput__countryCode {
  background: linear-gradient(135deg, hsl(247, 21%, 39%) 0%, hsl(210 30% 16%) 100%) !important;
  border: 1px solid hsl(210 40% 25%) !important;
  color: hsl(210 40% 96%) !important;
  box-shadow: 
    inset 0 1px 2px rgba(0, 0, 0, 0.1),
    0 0 0 1px hsl(210 50% 20%),
    0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.dark .clerk-wrapper .cl-formFieldInput:focus {
  background: linear-gradient(135deg, hsl(212, 22%, 32%) 0%, hsl(174 30% 18%) 100%) !important;
  border-color: hsl(174 60% 40%) !important;
  box-shadow: 
    0 0 0 3px hsl(174 60% 40% / 0.2),
    inset 0 1px 2px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(20, 184, 166, 0.15) !important;
}

.dark .clerk-wrapper .cl-socialButtonsBlockButton {
  background: linear-gradient(135deg, hsl(209, 54%, 35%) 0%, hsl(230, 51%, 54%) 100%) !important;
  border: 1px solid hsl(210, 100%, 97%) !important;
  color: hsl(210 40% 96%) !important;
  box-shadow: 
    inset 0 1px 2px rgba(0, 0, 0, 0.1),
    0 0 0 1px hsl(210 50% 20%),
    0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.dark .clerk-wrapper .cl-formFieldLabel {
  color: hsl(var(--foreground)) !important;
}

.dark .clerk-wrapper .cl-headerTitle {
  color: hsl(var(--foreground)) !important;
}

.dark .clerk-wrapper .cl-headerSubtitle {
  color: hsl(var(--muted-foreground)) !important;
}

.dark .clerk-wrapper .cl-socialButtonsBlockButton:hover {
  background: linear-gradient(135deg, hsl(209, 25%, 67%) 0%, hsl(174, 27%, 77%) 100%) !important;
  border-color: hsl(243, 100%, 54%) !important;
  box-shadow: 
    0 0 0 1px hsl(170, 5%, 75%),
    0 2px 8px rgba(20, 184, 166, 0.1),
    inset 0 1px 0 hsl(210 50% 25%) !important;
  transform: translateY(-1px) !important;
}

.dark .clerk-wrapper .cl-dividerLine {
  background: linear-gradient(90deg, transparent 0%, hsl(210, 68%, 49%) 50%, transparent 100%) !important;
  height: 1px !important;
}

.dark .clerk-wrapper .cl-dividerText {
  color: hsl(var(--muted-foreground)) !important;
  background-color: hsl(var(--background)) !important;
}

.dark .clerk-wrapper .cl-identityPreviewText {
  color: hsl(var(--foreground)) !important;
}

.dark .clerk-wrapper .cl-formFieldHintText {
  color: hsl(var(--muted-foreground)) !important;
}

.dark .clerk-wrapper .cl-formFieldErrorText {
  color: hsl(0 84.2% 70%) !important;
}

/* Additional dark mode improvements */
.dark .clerk-wrapper .cl-main {
  background-color: transparent !important;
}

.dark .clerk-wrapper .cl-internal-1w8pstk {
  color: hsl(var(--foreground)) !important;
}

.dark .clerk-wrapper .cl-loading {
  color: hsl(var(--foreground)) !important;
}

.dark .clerk-wrapper .cl-logoBox {
  filter: brightness(1.2) !important;
}

.dark .clerk-wrapper .cl-modalBackdrop {
  background-color: hsl(222.2 84% 4.9% / 0.8) !important;
}

.dark .clerk-wrapper [data-localization-key] {
  color: hsl(var(--foreground)) !important;
}

/* Improve button contrast in dark mode */
.dark .clerk-wrapper .cl-formButtonPrimary:disabled {
  background-color: hsl(var(--muted)) !important;
  color: hsl(var(--muted-foreground)) !important;
}

.dark .clerk-wrapper .cl-alternativeMethodsBlockButton {
  background-color: hsl(210 40% 20%) !important;
  border-color: hsl(209, 38%, 85%) !important;
  color: hsl(var(--foreground)) !important;
}

.dark .clerk-wrapper .cl-alternativeMethodsBlockButton:hover {
  background-color: hsl(0, 0%, 78%) !important;
}

/* Premium Button Design */
.clerk-wrapper .cl-formButtonPrimary {
  background: linear-gradient(135deg, hsl(174 70% 45%) 0%, hsl(174 60% 35%) 50%, hsl(174 80% 40%) 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: calc(var(--radius) - 2px) !important;
  padding: 0.5rem 1rem !important;
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  height: 44px !important;
  width: 100% !important;
  position: relative !important;
  overflow: hidden !important;
  box-shadow: 
    0 4px 14px rgba(20, 184, 166, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.clerk-wrapper .cl-formButtonPrimary::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg, transparent, rgba(251, 215, 215, 0.3), transparent) !important;
  transition: left 0.6s ease !important;
}

.clerk-wrapper .cl-formButtonPrimary:hover {
  background: linear-gradient(135deg, hsl(180, 100%, 100%) 0%, hsl(0, 0%, 100%) 50%, hsl(174 90% 45%) 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 
    0 6px 20px rgba(20, 184, 166, 0.35),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.clerk-wrapper .cl-formButtonPrimary:hover::before {
  left: 100% !important;
}

.clerk-wrapper .cl-formButtonPrimary:focus {
  box-shadow: 
    0 0 0 3px hsl(174 60% 50% / 0.4),
    0 6px 20px rgba(20, 184, 166, 0.35) !important;
  outline: none !important;
}

.clerk-wrapper .cl-formButtonPrimary:active {
  transform: translateY(-1px) !important;
}

/* Premium Light Mode Design */
.clerk-wrapper .cl-card {
  background: linear-gradient(135deg, hsl(0 0% 100%) 0%, hsl(210 50% 98%) 50%, hsl(210 30% 96%) 100%) !important;
  border: 1px solid hsl(210 20% 88%) !important;
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.06),
    0 0 0 1px hsl(210 20% 92%),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

.clerk-wrapper .cl-formFieldInput {
  background: linear-gradient(135deg, hsl(0 0% 100%) 0%, hsl(210 50% 99%) 100%) !important;
  border: 1px solid hsl(210 20% 85%) !important;
  color: hsl(var(--foreground)) !important;
  border-width: 1px !important;
  border-radius: calc(var(--radius) - 2px) !important;
  padding: 0.5rem 0.75rem !important;
  font-size: 0.875rem !important;
  transition: all 0.2s ease !important;
  box-shadow: 
    inset 0 1px 2px rgba(0, 0, 0, 0.02),
    0 0 0 1px hsl(210 20% 90%),
    0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

.clerk-wrapper .cl-formFieldInput:focus {
  border-color: hsl(var(--primary)) !important;
  box-shadow: 
    0 0 0 3px hsl(var(--primary) / 0.15),
    inset 0 1px 2px rgba(0, 0, 0, 0.02),
    0 2px 8px rgba(20, 184, 166, 0.1) !important;
  background: linear-gradient(135deg, hsl(0 0% 100%) 0%, hsl(174 30% 98%) 100%) !important;
  outline: none !important;
}

.clerk-wrapper .cl-formFieldInput::placeholder {
  color: hsl(var(--muted-foreground)) !important;
}

/* Enhanced social button styling for light mode */
.clerk-wrapper .cl-socialButtonsBlockButton {
  background: linear-gradient(135deg, hsl(0 0% 100%) 0%, hsl(210 50% 99%) 100%) !important;
  border: 1px solid hsl(210 20% 85%) !important;
  color: hsl(var(--foreground)) !important;
  border-radius: calc(var(--radius) - 2px) !important;
  padding: 0.5rem !important;
  transition: all 0.3s ease !important;
  height: 44px !important;
  box-shadow: 
    inset 0 1px 2px rgba(0, 0, 0, 0.02),
    0 0 0 1px hsl(210 20% 90%),
    0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

.clerk-wrapper .cl-socialButtonsBlockButton:hover {
  background: linear-gradient(135deg, hsl(174 30% 98%) 0%, hsl(210 50% 96%) 100%) !important;
  border-color: hsl(174 40% 75%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 
    0 0 0 1px hsl(174 30% 80%),
    0 2px 8px rgba(20, 184, 166, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

/* Form field labels */
.clerk-wrapper .cl-formFieldLabel {
  color: hsl(var(--foreground)) !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  margin-bottom: 0.5rem !important;
}

/* Header styling */
.clerk-wrapper .cl-headerTitle {
  color: hsl(var(--foreground)) !important;
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  margin-bottom: 0.5rem !important;
}

.clerk-wrapper .cl-headerSubtitle {
  color: hsl(var(--muted-foreground)) !important;
  font-size: 0.875rem !important;
}

/* Form error styling */
.clerk-wrapper .cl-formFieldErrorText {
  color: hsl(var(--destructive)) !important;
  font-size: 0.75rem !important;
  margin-top: 0.25rem !important;
}

/* Form hints */
.clerk-wrapper .cl-formFieldHintText {
  color: hsl(var(--muted-foreground)) !important;
  font-size: 0.75rem !important;
}

/* Divider styling */
.clerk-wrapper .cl-dividerLine {
  background-color: hsl(var(--border)) !important;
  height: 1px !important;
}

.clerk-wrapper .cl-dividerText {
  color: hsl(var(--muted-foreground)) !important;
  background-color: hsl(var(--card)) !important;
  font-size: 0.75rem !important;
  padding: 0 1rem !important;
}

/* Footer styling */
.clerk-wrapper .cl-footerActionText {
  color: hsl(var(--muted-foreground)) !important;
  font-size: 0.875rem !important;
}

.clerk-wrapper .cl-footerActionLink {
  color: hsl(var(--primary)) !important;
  font-weight: 500 !important;
  text-decoration: none !important;
}

.clerk-wrapper .cl-footerActionLink:hover {
  color: hsl(var(--primary) / 0.8) !important;
  text-decoration: underline !important;
}

/* Premium Typography Enhancements */
.clerk-wrapper .cl-headerTitle {
  background: linear-gradient(135deg, hsl(210 50% 25%) 0%, hsl(174 60% 35%) 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  font-weight: 700 !important;
  letter-spacing: -0.025em !important;
}

.dark .clerk-wrapper .cl-headerTitle {
  background: linear-gradient(135deg, hsl(210 40% 90%) 0%, hsl(174 60% 70%) 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

/* Enhanced Error States */
.clerk-wrapper .cl-formFieldError {
  background: linear-gradient(135deg, hsl(0 100% 97%) 0%, hsl(0 50% 95%) 100%) !important;
  border: 1px solid hsl(0 70% 85%) !important;
}

.dark .clerk-wrapper .cl-formFieldError {
  background: linear-gradient(135deg, hsl(0, 10%, 62%) 0%, hsl(0, 16%, 65%) 100%) !important;
  border: 1px solid hsl(0 60% 25%) !important;
}

/* Loading States */
.clerk-wrapper .cl-spinner {
  border-color: hsl(174 60% 50%) transparent hsl(174 60% 50%) transparent !important;
}

/* Hide only specific Clerk branding elements */
.clerk-wrapper .cl-internal-b3fm6y {
  display: none !important;
}

/* Alternative: Style it nicely if we want to keep it */
.clerk-wrapper .cl-brandingRoot {
  background: transparent !important;
  border: none !important;
  padding: 1rem 0 0 0 !important;
}

.clerk-wrapper .cl-brandingText {
  color: hsl(var(--muted-foreground)) !important;
  font-size: 0.75rem !important;
  opacity: 0.6 !important;
}

.dark .clerk-wrapper .cl-brandingText {
  color: hsl(210 40% 60%) !important;
}

/* More targeted branding removal - only hide very specific elements */
.clerk-wrapper a[href*="clerk.com"] {
  opacity: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
}

/* Only hide elements with very specific black styling that contain branding */
.clerk-wrapper div[style*="background-color: rgb(0, 0, 0)"]:has(a[href*="clerk"]) {
  display: none !important;
}

@layer components {
  .healthcare-card {
    /* Modern layered drop shadow with teal glow for depth */
    box-shadow:
      0 1px 3px 0 rgba(0, 0, 0, 0.1),
      0 1px 2px 0 rgba(0, 0, 0, 0.06),
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(20, 184, 166, 0.05),
      0 0 20px rgba(20, 184, 166, 0.08);

    /* Smooth transition for all shadow and transform effects */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* Subtle transform for better hover feedback */
    transform: translateY(0);
  }

  .healthcare-card:hover {
    /* Enhanced shadow with stronger teal glow on hover for premium feel */
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06),
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(20, 184, 166, 0.15),
      0 0 30px rgba(20, 184, 166, 0.2),
      0 0 60px rgba(20, 184, 166, 0.1);

    /* Subtle lift effect */
    transform: translateY(-4px);
  }

  /* Enhanced shadow with teal glow for dark mode */
  .dark .healthcare-card {
    box-shadow:
      0 1px 3px 0 rgba(0, 0, 0, 0.2),
      0 1px 2px 0 rgba(0, 0, 0, 0.12),
      0 4px 6px -1px rgba(0, 0, 0, 0.2),
      0 0 0 1px rgba(20, 184, 166, 0.1),
      0 0 25px rgba(20, 184, 166, 0.15);
  }

  .dark .healthcare-card:hover {
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.2),
      0 2px 4px -1px rgba(0, 0, 0, 0.12),
      0 10px 15px -3px rgba(0, 0, 0, 0.2),
      0 20px 25px -5px rgba(0, 0, 0, 0.25),
      0 0 0 1px rgba(20, 184, 166, 0.2),
      0 0 40px rgba(20, 184, 166, 0.25),
      0 0 80px rgba(20, 184, 166, 0.15);
  }
}

.cl-footer {
  display: none !important;
}

@theme inline {
  @keyframes border-beam {
  100% {
    offset-distance: 100%;
    }
  }
  @keyframes shine-pulse {
  0% {
    background-position: 0% 0%;
    }
  50% {
    background-position: 100% 100%;
    }
  to {
    background-position: 0% 0%;
    }
  }
}