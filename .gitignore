# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules

# next.js
/.next/
/out/

# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
sample.wav
docs/Gemini-Live-API.txt
/docs/

prompt_testing/

notes.md

scratchpad.txt

app/api/test-mae-email/route.ts

test/

.mcp.json

.claude/**
.claude/settings.local.json
