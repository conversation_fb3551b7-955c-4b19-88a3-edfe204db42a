/**
 * FORCE MAE TO UPDATE UI - ABSOLUTE LAST RESORT
 */

// Global state that UI components can access
let globalUIState = {
  userFields: {} as Record<string, any>,
  familyMembers: [] as any[],
  currentStep: 'user_info' as string,
  messages: ['<PERSON>: Ready to help!'] as string[]
}

let uiUpdateCallbacks: Array<(state: any) => void> = []

// Force UI update function that <PERSON> can call directly
export function FORCE_MAE_UI_UPDATE(type: 'fill_field' | 'submit_profile' | 'add_family', data: any) {
  console.log('🔥🔥🔥 FORCE MAE UI UPDATE:', type, data)
  
  switch (type) {
    case 'fill_field':
      globalUIState.userFields[data.field] = data.value
      globalUIState.messages.push(`Mae: Added ${data.field}: ${data.value}`)
      break
      
    case 'submit_profile':
      globalUIState.currentStep = 'family_info'
      globalUIState.messages.push('Mae: Moving to family setup!')
      break
      
    case 'add_family':
      globalUIState.familyMembers.push(data)
      globalUIState.messages.push(`Mae: Added ${data.name}`)
      break
  }
  
  // Notify all UI components
  uiUpdateCallbacks.forEach(callback => {
    try {
      callback({ ...globalUIState })
    } catch (error) {
      console.error('UI callback error:', error)
    }
  })
}

export function subscribeToMaeUI(callback: (state: any) => void): () => void {
  uiUpdateCallbacks.push(callback)
  
  // Send current state immediately
  callback({ ...globalUIState })
  
  return () => {
    const index = uiUpdateCallbacks.indexOf(callback)
    if (index > -1) {
      uiUpdateCallbacks.splice(index, 1)
    }
  }
}

export function getMaeUIState() {
  return { ...globalUIState }
}

// Make available globally for Mae to call
if (typeof window !== 'undefined') {
  (window as any).FORCE_MAE_UI_UPDATE = FORCE_MAE_UI_UPDATE
  console.log('🔥🔥🔥 FORCE_MAE_UI_UPDATE AVAILABLE GLOBALLY')
}