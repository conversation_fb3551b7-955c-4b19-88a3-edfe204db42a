/**
 * Mae AG-UI Function Tools
 * 
 * This module provides <PERSON> with comprehensive functions for interacting with users
 * through the AG-UI system for dynamic onboarding and profile management
 */

import {
  maeFilluserProfileField,
  maeSubmitUserProfile,
  maeAddFamilyMember,
  maeUpdateOnboardingProgress,
  maeGuideOnboardingFlow,
  maeGenerateUserProfileUI,
  maeGenerateFamilyMemberUI
} from '@/lib/mae-agui-integration'
import {
  getCompleteUserProfile,
  getCompleteFamilyMember,
  getOnboardingProgress,
  updateCompleteUserProfile,
  type CompleteUserProfile,
  type CompleteFamilyMember
} from '@/lib/mae-complete-profile-tools'

/**
 * <PERSON>'s Primary User Profile Management Functions
 * These functions allow <PERSON> to guide users through profile completion with dynamic UI
 */

export async function fillUserProfileField(args: {
  userId: string
  field: keyof CompleteUserProfile
  value: any
  generateUI?: boolean
}) {
  const { userId, field, value } = args
  
  try {
    // Update the database
    const updateData = { [field]: value } as Partial<CompleteUserProfile>
    await updateCompleteUserProfile(updateData)

    // FORCE DIRECT UI UPDATE - BYPASS ALL EVENT BULLSHIT
    const { maeUIBridge } = await import('@/lib/mae-direct-ui-bridge')
    maeUIBridge.fillUserField(String(field), value)
    
    // ALSO FORCE GLOBAL UI UPDATE - ULTIMATE BACKUP
    const { FORCE_MAE_UI_UPDATE } = await import('@/lib/mae-force-ui')
    FORCE_MAE_UI_UPDATE('fill_field', { field: String(field), value })
    
    // CRITICAL: Also try the AG-UI event - ONBOARDING PAGE DEPENDS ON THIS
    try {
      const { aguiEventDispatcher } = await import('@/lib/ag-ui-events')
      const fieldMapping: Record<string, string> = {
        'email': 'email', 'full_name': 'name', 'display_name': 'name', 'first_name': 'name', 'last_name': 'name',
        'phone_number': 'phone', 'zip_code': 'zip', 'date_of_birth': 'date_of_birth',
        'emergency_contact_name': 'emergency_contact', 'emergency_contact_phone': 'emergency_contact', 'role': 'role'
      }
      const aguiField = fieldMapping[String(field)] || String(field)
      console.log('🔥 Mae TRIGGERING AG-UI FILL USER FORM EVENT:', aguiField, value)
      await aguiEventDispatcher.fillUserForm({ field: aguiField as any, value, validate: true })
    } catch (error) {
      console.error('🚨 Failed to trigger AG-UI fillUserForm event:', error)
    }

    return {
      success: true,
      message: `FORCE UPDATED UI: ${String(field)} = ${value}`,
      field: String(field),
      value,
      direct_ui_update: true
    }

  } catch (error) {
    return {
      success: false,
      message: `Error filling field ${String(field)}`,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function submitUserProfileForm(args: {
  userId: string
  skipValidation?: boolean
  moveToFamilySection?: boolean
}) {
  const { userId, moveToFamilySection = true } = args
  
  try {
    // FORCE DIRECT UI UPDATE TO MOVE TO FAMILY STEP
    const { maeUIBridge } = await import('@/lib/mae-direct-ui-bridge')
    if (moveToFamilySection) {
      maeUIBridge.submitUserProfile()
    }
    
    // ALSO FORCE GLOBAL UI UPDATE
    const { FORCE_MAE_UI_UPDATE } = await import('@/lib/mae-force-ui')
    if (moveToFamilySection) {
      FORCE_MAE_UI_UPDATE('submit_profile', {})
    }
    
    // CRITICAL: Also try AG-UI events - ONBOARDING PAGE DEPENDS ON THIS
    try {
      const { aguiEventDispatcher } = await import('@/lib/ag-ui-events')
      console.log('🔥 Mae TRIGGERING AG-UI SUBMIT USER FORM EVENT')
      await aguiEventDispatcher.submitUserForm({ validate: true })
      if (moveToFamilySection) {
        console.log('🔥 Mae TRIGGERING AG-UI UPDATE PROGRESS EVENT: family_info')
        await aguiEventDispatcher.updateProgress({ step: 'family_info' })
      }
    } catch (error) {
      console.error('🚨 Failed to trigger AG-UI submit/progress events:', error)
    }

    return {
      success: true,
      message: 'FORCE MOVED TO FAMILY STEP!',
      next_step: 'family_members',
      direct_ui_update: true
    }

  } catch (error) {
    return {
      success: false,
      message: 'Error submitting user profile',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function addFamilyMemberWithUI(args: {
  userId: string
  name: string
  dateOfBirth: string
  relationship: string
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say'
  medicalConditions?: string[]
  allergies?: string[]
  medications?: Array<{ name: string; dosage: string; frequency: string }>
  additionalNotes?: string
  isPrimary?: boolean
}) {
  try {
    // FORCE DIRECT UI UPDATE TO ADD FAMILY MEMBER
    const { maeUIBridge } = await import('@/lib/mae-direct-ui-bridge')
    const member = {
      name: args.name,
      date_of_birth: args.dateOfBirth,
      relationship: args.relationship,
      gender: args.gender,
      medical_conditions: args.medicalConditions || [],
      allergies: args.allergies || [],
      medications: args.medications || [],
      additional_notes: args.additionalNotes
    }
    maeUIBridge.addFamilyMember(member)
    
    // ALSO FORCE GLOBAL UI UPDATE
    const { FORCE_MAE_UI_UPDATE } = await import('@/lib/mae-force-ui')
    FORCE_MAE_UI_UPDATE('add_family', member)
    
    // Also try AG-UI event as backup
    try {
      const { aguiEventDispatcher } = await import('@/lib/ag-ui-events')
      await aguiEventDispatcher.addFamilyMember({ ...member, is_primary: args.isPrimary || false })
    } catch {}

    return {
      success: true,
      message: `FORCE ADDED FAMILY MEMBER: ${args.name}`,
      family_member: member,
      direct_ui_update: true
    }

  } catch (error) {
    return {
      success: false,
      message: `Error adding family member ${args.name}`,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function guideUserThroughOnboarding(args: {
  userId: string
  currentStep?: 'welcome' | 'user_info' | 'family_info' | 'complete'
}) {
  const { userId, currentStep } = args
  
  console.log(`🤖 Mae guiding user ${userId} through onboarding. Current step: ${currentStep}`)
  
  try {
    // Get comprehensive flow guidance
    const flowResult = await maeGuideOnboardingFlow(userId)
    
    if (!flowResult.success) {
      return {
        success: false,
        message: 'Failed to guide onboarding flow',
        error: flowResult.error
      }
    }

    // Generate appropriate UI based on current step
    let uiInstructions = null
    if (flowResult.current_step === 'user_info') {
      const userUIResult = await maeGenerateUserProfileUI(userId)
      if (userUIResult.success) {
        uiInstructions = userUIResult.ui_instructions
      }
    } else if (flowResult.current_step === 'family_info') {
      const familyUIResult = await maeGenerateFamilyMemberUI(userId)
      if (familyUIResult.success) {
        uiInstructions = familyUIResult.ui_instructions
      }
    }

    return {
      success: true,
      message: flowResult.instructions,
      current_step: flowResult.current_step,
      completion: flowResult.completion,
      missing_fields: flowResult.missing_fields,
      family_count: flowResult.family_count,
      ui_instructions: uiInstructions,
      ui_focus: flowResult.ui_focus,
      recommendations: generateOnboardingRecommendations(flowResult)
    }

  } catch (error) {
    console.error(`🚨 Mae error guiding onboarding:`, error)
    return {
      success: false,
      message: 'Error guiding onboarding flow',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function getOnboardingStatus(args: { clerkId: string }) {
  const { clerkId } = args
  
  console.log(`🤖 Mae checking onboarding status for user ${clerkId}`)
  
  try {
    const progress = await getOnboardingProgress(clerkId)
    const profile = await getCompleteUserProfile(clerkId)
    
    return {
      success: true,
      user_profile_completion: Math.round(progress.data!.user_profile_completion * 100),
      family_members_count: progress.data!.family_members_count,
      missing_user_fields: progress.data!.required_fields.missing,
      profile_summary: {
        name: profile.data?.display_name || profile.data?.name || 'Not provided',
        email: profile.data?.email || 'Not provided',
        phone: profile.data?.phone || 'Not provided',
        emergency_contact: profile.data?.emergency_contact || 'Not provided'
      },
      next_recommended_action: determineNextAction(progress.data!),
      estimated_completion_time: estimateCompletionTime(progress.data!)
    }

  } catch (error) {
    console.error(`🚨 Mae error checking onboarding status:`, error)
    return {
      success: false,
      message: 'Error checking onboarding status',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function generatePersonalizedOnboardingMessage(args: { clerkId: string }) {
  const { clerkId } = args
  
  console.log(`🤖 Mae generating personalized onboarding message for user ${clerkId}`)
  
  try {
    const progress = await getOnboardingProgress(clerkId)
    const profile = await getCompleteUserProfile(clerkId)
    
    let message = ''
    let tone = ''
    let nextStep = ''

    if (progress.data!.user_profile_completion < 0.3) {
      tone = 'welcoming'
      message = `Hi there! I'm Mae, your AI parenting assistant. I'm excited to get to know you better so I can provide personalized guidance for your family. Let's start with some basic information about you.`
      nextStep = 'Let\'s begin by getting your name and contact information.'
    } else if (progress.data!.user_profile_completion < 0.8) {
      tone = 'encouraging'
      const name = profile.data?.display_name || profile.data?.name || 'there'
      message = `Great progress, ${name}! We're almost done with your profile. Just a few more details and then we can move on to adding your family members.`
      nextStep = `We still need: ${progress.data!.required_fields.missing.slice(0, 3).join(', ')}`
    } else if (progress.data!.family_members_count === 0) {
      tone = 'transitioning'
      const name = profile.data?.display_name || profile.data?.name || 'there'
      message = `Perfect, ${name}! Your profile is all set. Now let's add your family members so I can give you personalized advice for each of them.`
      nextStep = 'Would you like to start by adding your first family member?'
    } else {
      tone = 'completing'
      const name = profile.data?.display_name || profile.data?.name || 'there'
      message = `Excellent, ${name}! You have ${progress.data!.family_members_count} family member${progress.data!.family_members_count > 1 ? 's' : ''} in your profile. Your onboarding is complete and I'm ready to help with personalized parenting guidance!`
      nextStep = 'You can start asking me questions about parenting, child development, or managing family life.'
    }

    return {
      success: true,
      message,
      tone,
      next_step: nextStep,
      completion_percentage: Math.round(progress.data!.user_profile_completion * 100),
      family_count: progress.data!.family_members_count,
      personalization: {
        user_name: profile.data?.display_name || profile.data?.name || null,
        has_emergency_contact: !!(profile.data?.emergency_contact),
        profile_completeness: progress.data!.user_profile_completion
      }
    }

  } catch (error) {
    console.error(`🚨 Mae error generating personalized message:`, error)
    return {
      success: false,
      message: 'Error generating personalized message',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// Helper functions

function generateOnboardingRecommendations(flowResult: any) {
  const recommendations = []
  
  if (flowResult.current_step === 'user_info') {
    recommendations.push('Complete your profile to unlock personalized parenting advice')
    if (flowResult.missing_fields?.includes('emergency_contact_name')) {
      recommendations.push('Adding an emergency contact is important for safety')
    }
    if (flowResult.missing_fields?.includes('zip_code')) {
      recommendations.push('Your ZIP code helps me find local resources and services')
    }
  } else if (flowResult.current_step === 'family_info') {
    recommendations.push('Add family members to get age-appropriate guidance')
    recommendations.push('Include medical conditions and allergies for safety alerts')
  }
  
  return recommendations
}

function determineNextAction(progress: any): string {
  if (progress.user_profile_completion < 0.8) {
    return 'complete_user_profile'
  } else if (progress.family_members_count === 0) {
    return 'add_first_family_member'
  } else {
    return 'onboarding_complete'
  }
}

function estimateCompletionTime(progress: any): string {
  const userTime = progress.user_profile_completion < 0.8 ? 3 : 0
  const familyTime = progress.family_members_count === 0 ? 5 : 0
  const totalMinutes = userTime + familyTime
  
  if (totalMinutes === 0) return 'Complete!'
  if (totalMinutes <= 3) return '2-3 minutes'
  if (totalMinutes <= 6) return '5-6 minutes'
  return '8-10 minutes'
}