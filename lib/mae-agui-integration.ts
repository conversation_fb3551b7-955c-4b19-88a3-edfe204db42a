/**
 * Mae AG-UI Integration Module
 * 
 * This module provides <PERSON> with the ability to dynamically generate UI elements
 * through the AG-UI event system for onboarding and profile completion
 */

import { 
  aguiEventDispatcher, 
  type FillUserFormPayload, 
  type SubmitUserFormPayload, 
  type AddFamilyMemberPayload, 
  type OnboardingProgressPayload 
} from '@/lib/ag-ui-events'
import { 
  getCompleteUserProfile, 
  getCompleteFamilyMember, 
  getOnboardingProgress, 
  type CompleteUserProfile, 
  type CompleteFamilyMember 
} from '@/lib/mae-complete-profile-tools'

/**
 * <PERSON>'s AG-UI Field Filling Functions
 * These functions allow <PERSON> to fill form fields through voice interaction
 */

export async function maeFilluserProfileField(field: keyof CompleteUserProfile, value: any, userId: string) {
  console.log(`🎯 Mae filling user profile field: ${String(field)} = ${value}`)
  
  try {
    // Map database fields to AG-UI form fields
    const fieldMapping: Record<string, string> = {
      'email': 'email',
      'full_name': 'name',
      'display_name': 'name',
      'first_name': 'name',
      'last_name': 'name',
      'phone_number': 'phone',
      'zip_code': 'zip',
      'date_of_birth': 'date_of_birth',
      'emergency_contact_name': 'emergency_contact',
      'emergency_contact_phone': 'emergency_contact',
      'role': 'role'
    }

    const aguiField = fieldMapping[String(field)]
    if (!aguiField) {
      console.warn(`🎯 No AG-UI mapping for field: ${String(field)}`)
      return { success: false, error: `Field ${String(field)} not supported in AG-UI` }
    }

    // Format emergency contact as object if needed
    let formattedValue = value
    if (String(field).includes('emergency_contact')) {
      const profile = await getCompleteUserProfile(userId)
      formattedValue = {
        name: String(field) === 'emergency_contact_name' ? value : profile?.emergency_contact_name || '',
        phone: String(field) === 'emergency_contact_phone' ? value : profile?.emergency_contact_phone || ''
      }
    }

    const payload: FillUserFormPayload = {
      field: aguiField as any,
      value: formattedValue,
      validate: true
    }

    const response = await aguiEventDispatcher.fillUserForm(payload)
    
    console.log(`🎯 AG-UI fillUserForm response:`, response)
    return response

  } catch (error) {
    console.error(`🚨 Error filling user profile field ${String(field)}:`, error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function maeSubmitUserProfile(userId: string, skipValidation = false) {
  console.log(`🎯 Mae submitting user profile for user: ${userId}`)
  
  try {
    const payload: SubmitUserFormPayload = {
      validate: !skipValidation,
      skipValidation
    }

    const response = await aguiEventDispatcher.submitUserForm(payload)
    
    console.log(`🎯 AG-UI submitUserForm response:`, response)
    return response

  } catch (error) {
    console.error(`🚨 Error submitting user profile:`, error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function maeAddFamilyMember(familyMemberData: CompleteFamilyMember, userId: string) {
  console.log(`🎯 Mae adding family member: ${familyMemberData.name}`)
  
  try {
    const payload: AddFamilyMemberPayload = {
      name: familyMemberData.name,
      date_of_birth: familyMemberData.date_of_birth,
      gender: familyMemberData.gender as any,
      relationship: familyMemberData.relationship,
      medical_conditions: familyMemberData.medical_conditions || [],
      allergies: familyMemberData.allergies || [],
      medications: familyMemberData.medications ? 
        familyMemberData.medications.map(med => ({
          name: med.name || '',
          dosage: med.dosage || '',
          frequency: med.frequency || ''
        })) : [],
      additional_notes: familyMemberData.notes,
      is_primary: familyMemberData.is_primary_child || false
    }

    const response = await aguiEventDispatcher.addFamilyMember(payload)
    
    console.log(`🎯 AG-UI addFamilyMember response:`, response)
    return response

  } catch (error) {
    console.error(`🚨 Error adding family member:`, error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function maeUpdateOnboardingProgress(step: 'welcome' | 'user_info' | 'family_info' | 'complete', userId: string, data?: any) {
  console.log(`🎯 Mae updating onboarding progress to: ${step}`)
  
  try {
    const payload: OnboardingProgressPayload = {
      step,
      data
    }

    const response = await aguiEventDispatcher.updateProgress(payload)
    
    console.log(`🎯 AG-UI updateProgress response:`, response)
    return response

  } catch (error) {
    console.error(`🚨 Error updating onboarding progress:`, error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

/**
 * Mae's Smart Onboarding Flow Controller
 * This function analyzes the user's profile completion and guides the onboarding process
 */

export async function maeGuideOnboardingFlow(userId: string) {
  console.log(`🎯 Mae guiding onboarding flow for user: ${userId}`)
  
  try {
    // Get current progress
    const progress = await getOnboardingProgress(userId)
    console.log(`🎯 Current onboarding progress:`, progress)

    // Determine next step based on completion
    if (progress.user_profile_completion < 0.8) {
      // Focus on user profile completion
      await maeUpdateOnboardingProgress('user_info', userId, {
        completion: progress.user_profile_completion,
        missing_fields: progress.missing_user_fields
      })

      return {
        success: true,
        current_step: 'user_info',
        completion: progress.user_profile_completion,
        missing_fields: progress.missing_user_fields,
        instructions: 'Let\'s complete your profile first. I\'ll help you fill in the missing information.',
        ui_focus: 'user_profile_form'
      }

    } else if (progress.family_members_count === 0) {
      // Move to family member addition
      await maeUpdateOnboardingProgress('family_info', userId, {
        user_complete: true,
        family_count: progress.family_members_count
      })

      return {
        success: true,
        current_step: 'family_info',
        completion: 1.0,
        family_count: progress.family_members_count,
        instructions: 'Great! Your profile is complete. Now let\'s add your family members so I can provide personalized care guidance.',
        ui_focus: 'family_member_form'
      }

    } else {
      // Onboarding complete
      await maeUpdateOnboardingProgress('complete', userId, {
        user_complete: true,
        family_count: progress.family_members_count,
        total_completion: 1.0
      })

      return {
        success: true,
        current_step: 'complete',
        completion: 1.0,
        family_count: progress.family_members_count,
        instructions: 'Excellent! Your onboarding is complete. I\'m ready to help you with personalized parenting guidance.',
        ui_focus: 'dashboard'
      }
    }

  } catch (error) {
    console.error(`🚨 Error guiding onboarding flow:`, error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

/**
 * Mae's UI Generation Instructions
 * These functions provide Mae with specific instructions for generating UI elements
 */

export async function maeGenerateUserProfileUI(userId: string) {
  console.log(`🎯 Mae generating user profile UI for user: ${userId}`)
  
  try {
    const profile = await getCompleteUserProfile(userId)
    const progress = await getOnboardingProgress(userId)
    
    const uiInstructions = {
      form_type: 'user_profile',
      fields_to_display: progress.missing_user_fields.map(field => ({
        name: field,
        type: getFieldType(field),
        required: isFieldRequired(field),
        current_value: profile ? (profile as any)[field] : null,
        placeholder: getFieldPlaceholder(field)
      })),
      completion_percentage: Math.round(progress.user_profile_completion * 100),
      next_action: progress.user_profile_completion < 1 ? 'continue_profile' : 'move_to_family',
      validation_rules: getFieldValidationRules()
    }

    console.log(`🎯 Generated user profile UI instructions:`, uiInstructions)
    return { success: true, ui_instructions: uiInstructions }

  } catch (error) {
    console.error(`🚨 Error generating user profile UI:`, error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function maeGenerateFamilyMemberUI(userId: string) {
  console.log(`🎯 Mae generating family member UI for user: ${userId}`)
  
  try {
    const uiInstructions = {
      form_type: 'family_member',
      fields_to_display: [
        { name: 'name', type: 'text', required: true, placeholder: 'Enter family member\'s name' },
        { name: 'date_of_birth', type: 'date', required: true, placeholder: 'MM/DD/YYYY' },
        { name: 'relationship', type: 'select', required: true, options: ['child', 'spouse', 'parent', 'sibling', 'other'] },
        { name: 'gender', type: 'select', required: false, options: ['male', 'female', 'other', 'prefer_not_to_say'] },
        { name: 'medical_conditions', type: 'multi-text', required: false, placeholder: 'Any medical conditions (optional)' },
        { name: 'allergies', type: 'multi-text', required: false, placeholder: 'Any allergies (optional)' },
        { name: 'medications', type: 'medication-list', required: false, placeholder: 'Current medications (optional)' },
        { name: 'notes', type: 'textarea', required: false, placeholder: 'Additional notes (optional)' }
      ],
      next_action: 'add_family_member',
      can_add_more: true,
      validation_rules: getFamilyMemberValidationRules()
    }

    console.log(`🎯 Generated family member UI instructions:`, uiInstructions)
    return { success: true, ui_instructions: uiInstructions }

  } catch (error) {
    console.error(`🚨 Error generating family member UI:`, error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// Helper functions for UI generation

function getFieldType(field: string): string {
  const typeMapping: Record<string, string> = {
    'email': 'email',
    'phone_number': 'tel',
    'date_of_birth': 'date',
    'zip_code': 'text',
    'emergency_contact_name': 'text',
    'emergency_contact_phone': 'tel',
    'bio': 'textarea',
    'parenting_experience': 'select',
    'household_size': 'number',
    'role': 'select'
  }
  return typeMapping[field] || 'text'
}

function isFieldRequired(field: string): boolean {
  const requiredFields = ['email', 'full_name', 'display_name', 'first_name', 'last_name']
  return requiredFields.includes(field)
}

function getFieldPlaceholder(field: string): string {
  const placeholders: Record<string, string> = {
    'email': 'Enter your email address',
    'full_name': 'Enter your full name',
    'display_name': 'How would you like to be called?',
    'first_name': 'Enter your first name',
    'last_name': 'Enter your last name',
    'phone_number': 'Enter your phone number',
    'date_of_birth': 'MM/DD/YYYY',
    'zip_code': 'Enter your ZIP code',
    'emergency_contact_name': 'Emergency contact name',
    'emergency_contact_phone': 'Emergency contact phone',
    'bio': 'Tell us a bit about yourself',
    'parenting_experience': 'Select your parenting experience level',
    'household_size': 'Number of people in household',
    'role': 'Select your role'
  }
  return placeholders[field] || `Enter ${field.replace('_', ' ')}`
}

function getFieldValidationRules() {
  return {
    email: { pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$', message: 'Please enter a valid email address' },
    phone_number: { pattern: '^\\+?[\\d\\s\\-\\(\\)]+$', message: 'Please enter a valid phone number' },
    zip_code: { pattern: '^\\d{5}(-\\d{4})?$', message: 'Please enter a valid ZIP code' },
    date_of_birth: { custom: 'age_validation', message: 'You must be at least 18 years old' }
  }
}

function getFamilyMemberValidationRules() {
  return {
    name: { required: true, message: 'Name is required' },
    date_of_birth: { required: true, custom: 'date_validation', message: 'Please enter a valid date of birth' },
    relationship: { required: true, message: 'Relationship is required' }
  }
}