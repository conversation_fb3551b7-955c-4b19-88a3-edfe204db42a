/**
 * Direct UI Bridge for Mae
 * Bypasses the event system and directly updates UI state
 */

interface UIState {
  userFields: Record<string, any>
  familyMembers: any[]
  currentStep: 'user_info' | 'family_info' | 'complete'
  messages: string[]
}

class MaeUIBridge {
  private static instance: MaeUIBridge
  private uiState: UIState = {
    userFields: {},
    familyMembers: [],
    currentStep: 'user_info',
    messages: []
  }
  private listeners: Array<(state: UIState) => void> = []

  static getInstance(): MaeUIBridge {
    if (!MaeUIBridge.instance) {
      MaeUIBridge.instance = new MaeUIBridge()
    }
    return MaeUIBridge.instance
  }

  subscribe(listener: (state: UIState) => void): () => void {
    this.listeners.push(listener)
    return () => {
      const index = this.listeners.indexOf(listener)
      if (index > -1) {
        this.listeners.splice(index, 1)
      }
    }
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener({ ...this.uiState }))
  }

  fillUserField(field: string, value: any) {
    console.log('🔥 DIRECT UI UPDATE:', field, value)
    this.uiState.userFields[field] = value
    this.uiState.messages.push(`Mae: Added your ${field}: ${value}`)
    this.notifyListeners()
  }

  submitUserProfile() {
    console.log('🔥 DIRECT UI SUBMIT')
    this.uiState.currentStep = 'family_info'
    this.uiState.messages.push('Mae: Great! Moving to family setup...')
    this.notifyListeners()
  }

  addFamilyMember(member: any) {
    console.log('🔥 DIRECT UI ADD FAMILY:', member)
    this.uiState.familyMembers.push(member)
    this.uiState.messages.push(`Mae: Added ${member.name} to your family`)
    this.notifyListeners()
  }

  getState(): UIState {
    return { ...this.uiState }
  }

  reset() {
    this.uiState = {
      userFields: {},
      familyMembers: [],
      currentStep: 'user_info',
      messages: ['Mae: Ready to help with your onboarding!']
    }
    this.notifyListeners()
  }
}

export const maeUIBridge = MaeUIBridge.getInstance()

// Global window access for Mae API calls
if (typeof window !== 'undefined') {
  (window as any).maeUIBridge = maeUIBridge
}