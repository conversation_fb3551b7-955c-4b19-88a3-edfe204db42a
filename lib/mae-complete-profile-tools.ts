/**
 * Mae Complete Profile Management Tools
 * 
 * This module provides <PERSON> with comprehensive database field acknowledgment
 * and management for all user and family member fields from the database schema.
 */

import { createClient } from '@supabase/supabase-js'
import { currentUser } from '@clerk/nextjs/server'
import { aguiEventDispatcher } from '@/lib/ag-ui-events'

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Complete user profile type based on database schema
export interface CompleteUserProfile {
  // Core user fields (users table)
  id?: string
  email: string
  name: string
  image?: string
  role: 'parent' | 'child'
  phone?: string
  zip?: string
  family_id?: string
  preferences?: Record<string, any>
  bio?: string
  emergency_contact?: string
  date_of_birth?: string
  onboarding_completed?: boolean
  onboarding_step?: string
  clerk_id?: string
  
  // Extended profile fields (user_profiles table)
  display_name?: string
  avatar_url?: string
  location?: string
  website?: string
  social_links?: {
    facebook?: string
    instagram?: string
    twitter?: string
    linkedin?: string
    blog?: string
  }
  privacy_settings?: {
    profile_visibility?: 'public' | 'private' | 'friends_only'
    show_family_info?: boolean
    allow_contact?: boolean
    data_sharing?: 'minimal' | 'standard' | 'full'
    marketing_emails?: boolean
    care_reminders?: boolean
  }
  notification_preferences?: {
    email_notifications?: boolean
    push_notifications?: boolean
    care_reminders?: boolean
    family_updates?: boolean
    mae_insights?: boolean
    newsletter?: boolean
    appointment_reminders?: boolean
    medication_reminders?: boolean
    growth_milestones?: boolean
  }
  
  // Timestamps
  created_at?: string
  updated_at?: string
}

// Complete family member type based on database schema
export interface CompleteFamilyMember {
  id?: string
  user_id?: string
  name: string
  date_of_birth: string
  gender?: 'male' | 'female' | 'other'
  medical_conditions?: string[]
  allergies?: string[]
  medications?: string[]
  additional_notes?: string
  avatar?: string
  relationship: string
  is_primary?: boolean
  created_at?: string
  updated_at?: string
  
  // Calculated fields
  age?: number
  age_description?: string
}

/**
 * Get complete user profile acknowledging all database fields
 */
export async function getCompleteUserProfile(userId: string): Promise<{
  success: boolean
  data?: CompleteUserProfile
  error?: string
}> {
  try {
    const clerkUser = await currentUser()
    if (!clerkUser) {
      return {
        success: false,
        error: 'User not authenticated'
      }
    }

    // Get user data from users table
    const { data: user, error: userError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        name,
        image,
        role,
        phone,
        zip,
        family_id,
        preferences,
        bio,
        emergency_contact,
        date_of_birth,
        onboarding_completed,
        onboarding_step,
        clerk_id,
        created_at,
        updated_at
      `)
      .eq('clerk_id', clerkUser.id)
      .single()

    if (userError) {
      return {
        success: false,
        error: `User not found: ${userError.message}`
      }
    }

    // Get extended profile data from user_profiles table
    const { data: profile } = await supabase
      .from('user_profiles')
      .select(`
        display_name,
        avatar_url,
        location,
        website,
        social_links,
        privacy_settings,
        notification_preferences
      `)
      .eq('user_id', user.id)
      .single()

    // Combine all data into complete profile
    const completeProfile: CompleteUserProfile = {
      ...user,
      display_name: profile?.display_name,
      avatar_url: profile?.avatar_url,
      location: profile?.location,
      website: profile?.website,
      social_links: profile?.social_links || {},
      privacy_settings: profile?.privacy_settings || {},
      notification_preferences: profile?.notification_preferences || {}
    }

    return {
      success: true,
      data: completeProfile
    }
  } catch (error) {
    console.error('❌ Error getting complete user profile:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Update complete user profile with all field acknowledgment
 */
export async function updateCompleteUserProfile(updates: Partial<CompleteUserProfile>): Promise<{
  success: boolean
  data?: CompleteUserProfile
  error?: string
  message?: string
}> {
  try {
    const clerkUser = await currentUser()
    if (!clerkUser) {
      return {
        success: false,
        error: 'User not authenticated'
      }
    }

    // Get user ID first
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', clerkUser.id)
      .single()

    if (userError) {
      return {
        success: false,
        error: `User not found: ${userError.message}`
      }
    }

    // Separate updates for users table and user_profiles table
    const userTableFields = [
      'email', 'name', 'image', 'role', 'phone', 'zip', 'family_id',
      'preferences', 'bio', 'emergency_contact', 'date_of_birth',
      'onboarding_completed', 'onboarding_step'
    ]

    const profileTableFields = [
      'display_name', 'avatar_url', 'location', 'website',
      'social_links', 'privacy_settings', 'notification_preferences'
    ]

    // Build users table updates
    const userUpdates: Record<string, any> = {}
    userTableFields.forEach(field => {
      if (field in updates) {
        userUpdates[field] = updates[field as keyof CompleteUserProfile]
      }
    })

    // Build user_profiles table updates
    const profileUpdates: Record<string, any> = {}
    profileTableFields.forEach(field => {
      if (field in updates) {
        profileUpdates[field] = updates[field as keyof CompleteUserProfile]
      }
    })

    // Update users table if needed
    if (Object.keys(userUpdates).length > 0) {
      userUpdates.updated_at = new Date().toISOString()
      
      const { error: userUpdateError } = await supabase
        .from('users')
        .update(userUpdates)
        .eq('clerk_id', clerkUser.id)

      if (userUpdateError) {
        return {
          success: false,
          error: `Failed to update user data: ${userUpdateError.message}`
        }
      }
    }

    // Update user_profiles table if needed
    if (Object.keys(profileUpdates).length > 0) {
      profileUpdates.updated_at = new Date().toISOString()
      
      const { error: profileUpdateError } = await supabase
        .from('user_profiles')
        .upsert({
          user_id: user.id,
          ...profileUpdates
        })

      if (profileUpdateError) {
        return {
          success: false,
          error: `Failed to update profile data: ${profileUpdateError.message}`
        }
      }
    }

    // Get updated complete profile
    const updatedProfile = await getCompleteUserProfile(user.id)
    
    console.log('✅ Updated complete user profile')
    
    return {
      success: true,
      data: updatedProfile.data,
      message: 'Profile updated successfully'
    }
  } catch (error) {
    console.error('❌ Error updating complete user profile:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Get complete family member with all database fields
 */
export async function getCompleteFamilyMember(memberId: string): Promise<{
  success: boolean
  data?: CompleteFamilyMember
  error?: string
}> {
  try {
    const clerkUser = await currentUser()
    if (!clerkUser) {
      return {
        success: false,
        error: 'User not authenticated'
      }
    }

    // Get user ID first
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', clerkUser.id)
      .single()

    if (userError) {
      return {
        success: false,
        error: `User not found: ${userError.message}`
      }
    }

    // Get complete family member data
    const { data: familyMember, error: memberError } = await supabase
      .from('family_members')
      .select(`
        id,
        user_id,
        name,
        date_of_birth,
        gender,
        medical_conditions,
        allergies,
        medications,
        additional_notes,
        avatar,
        relationship,
        is_primary,
        created_at,
        updated_at
      `)
      .eq('id', memberId)
      .eq('user_id', user.id)
      .single()

    if (memberError) {
      return {
        success: false,
        error: `Family member not found: ${memberError.message}`
      }
    }

    // Calculate age
    const birthDate = new Date(familyMember.date_of_birth)
    const today = new Date()
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }

    const completeMember: CompleteFamilyMember = {
      ...familyMember,
      age,
      age_description: age < 1 
        ? `${Math.floor((today.getTime() - birthDate.getTime()) / (1000 * 60 * 60 * 24 * 30))} months old`
        : `${age} years old`
    }

    return {
      success: true,
      data: completeMember
    }
  } catch (error) {
    console.error('❌ Error getting complete family member:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Add complete family member with all field acknowledgment
 */
export async function addCompleteFamilyMember(familyData: Omit<CompleteFamilyMember, 'id' | 'user_id' | 'created_at' | 'updated_at' | 'age' | 'age_description'>): Promise<{
  success: boolean
  data?: CompleteFamilyMember
  error?: string
  message?: string
}> {
  try {
    const clerkUser = await currentUser()
    if (!clerkUser) {
      return {
        success: false,
        error: 'User not authenticated'
      }
    }

    // Get user ID first
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', clerkUser.id)
      .single()

    if (userError) {
      return {
        success: false,
        error: `User not found: ${userError.message}`
      }
    }

    // Validate required fields
    if (!familyData.name || !familyData.date_of_birth || !familyData.relationship) {
      return {
        success: false,
        error: 'Name, date of birth, and relationship are required'
      }
    }

    // Validate date of birth
    const dobDate = new Date(familyData.date_of_birth)
    if (dobDate > new Date()) {
      return {
        success: false,
        error: 'Date of birth cannot be in the future'
      }
    }

    // If this is being set as primary, unset other primary members
    if (familyData.is_primary) {
      await supabase
        .from('family_members')
        .update({ is_primary: false })
        .eq('user_id', user.id)
    }

    // Prepare complete family member data
    const completeFamilyData = {
      user_id: user.id,
      name: familyData.name,
      date_of_birth: familyData.date_of_birth,
      gender: familyData.gender || null,
      medical_conditions: familyData.medical_conditions || [],
      allergies: familyData.allergies || [],
      medications: familyData.medications || [],
      additional_notes: familyData.additional_notes || null,
      avatar: familyData.avatar || null,
      relationship: familyData.relationship,
      is_primary: familyData.is_primary || false
    }

    // Add family member
    const { data: newMember, error: insertError } = await supabase
      .from('family_members')
      .insert(completeFamilyData)
      .select()
      .single()

    if (insertError) {
      return {
        success: false,
        error: `Failed to add family member: ${insertError.message}`
      }
    }

    // Get complete member data with age calculation
    const completeResult = await getCompleteFamilyMember(newMember.id)
    
    console.log('✅ Added complete family member:', newMember.name)
    
    return {
      success: true,
      data: completeResult.data,
      message: `Successfully added ${newMember.name} to your family`
    }
  } catch (error) {
    console.error('❌ Error adding complete family member:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Get user's onboarding progress with field analysis
 */
export async function getOnboardingProgress(userId: string): Promise<{
  success: boolean
  data?: {
    user_profile_completion: number
    family_members_count: any
    current_step: string
    completed: boolean
    required_fields: {
      completed: string[]
      missing: string[]
    }
    family_setup: {
      members_count: number
      has_primary: boolean
    }
    completion_percentage: number
  }
  error?: string
}> {
  try {
    const profileResult = await getCompleteUserProfile(userId)
    if (!profileResult.success || !profileResult.data) {
      return {
        success: false,
        error: 'Could not get user profile'
      }
    }

    const profile = profileResult.data

    // Define required fields for onboarding
    const requiredFields = ['name', 'email', 'phone', 'zip']
    const recommendedFields = ['date_of_birth', 'emergency_contact', 'bio']

    // Check which fields are completed
    const completedFields: string[] = []
    const missingFields: string[] = []

    requiredFields.forEach(field => {
      const value = profile[field as keyof CompleteUserProfile]
      if (value && value !== '') {
        completedFields.push(field)
      } else {
        missingFields.push(field)
      }
    })

    // Check recommended fields
    recommendedFields.forEach(field => {
      const value = profile[field as keyof CompleteUserProfile]
      if (value && value !== '') {
        completedFields.push(field)
      }
    })

    // Get family member count
    const clerkUser = await currentUser()
    const { data: user } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', clerkUser!.id)
      .single()

    const { data: familyMembers } = await supabase
      .from('family_members')
      .select('id, is_primary')
      .eq('user_id', user!.id)

    const familyCount = familyMembers?.length || 0
    const hasPrimary = familyMembers?.some(m => m.is_primary) || false

    // Calculate completion percentage
    const totalRequiredFields = requiredFields.length
    const completedRequiredFields = requiredFields.filter(field => 
      completedFields.includes(field)
    ).length
    
    const fieldProgress = (completedRequiredFields / totalRequiredFields) * 70 // 70% weight for fields
    const familyProgress = familyCount > 0 ? 30 : 0 // 30% weight for family setup
    const completionPercentage = Math.round(fieldProgress + familyProgress)

    return {
      success: true,
      data: {
        user_profile_completion: completionPercentage / 100,
        family_members_count: familyCount,
        current_step: profile.onboarding_step || 'welcome',
        completed: profile.onboarding_completed || false,
        required_fields: {
          completed: completedFields,
          missing: missingFields
        },
        family_setup: {
          members_count: familyCount,
          has_primary: hasPrimary
        },
        completion_percentage: completionPercentage
      }
    }
  } catch (error) {
    console.error('❌ Error getting onboarding progress:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Mae function to generate dynamic UI based on missing fields
 */
export async function generateOnboardingUI(step: string): Promise<{
  success: boolean
  ui_instructions?: {
    step: string
    title: string
    description: string
    fields: Array<{
      name: string
      type: string
      label: string
      required: boolean
      placeholder?: string
      options?: string[]
      validation?: string
    }>
    next_step?: string
  }
  error?: string
}> {
  const userId = '123' // TODO: Get user ID from context
  try {
    const progressResult = await getOnboardingProgress(userId)
    if (!progressResult.success) {
      return {
        success: false,
        error: 'Could not determine onboarding progress'
      }
    }

    const progress = progressResult.data!

    // Generate UI instructions based on step and missing fields
    switch (step) {
      case 'user_info':
        return {
          success: true,
          ui_instructions: {
            step: 'user_info',
            title: 'Your Information',
            description: 'Let\'s start by getting to know you better',
            fields: [
              {
                name: 'name',
                type: 'text',
                label: 'Full Name',
                required: true,
                placeholder: 'Enter your full name'
              },
              {
                name: 'phone',
                type: 'tel',
                label: 'Phone Number',
                required: true,
                placeholder: '(*************'
              },
              {
                name: 'zip',
                type: 'text',
                label: 'ZIP Code',
                required: true,
                placeholder: '12345'
              },
              {
                name: 'date_of_birth',
                type: 'date',
                label: 'Date of Birth',
                required: false
              },
              {
                name: 'emergency_contact',
                type: 'text',
                label: 'Emergency Contact',
                required: false,
                placeholder: 'Name and phone number'
              }
            ],
            next_step: 'family_info'
          }
        }

      case 'family_info':
        return {
          success: true,
          ui_instructions: {
            step: 'family_info',
            title: 'Family Members',
            description: `You have ${progress.family_setup.members_count} family member${progress.family_setup.members_count !== 1 ? 's' : ''} added. Let's add your children or other family members.`,
            fields: [
              {
                name: 'name',
                type: 'text',
                label: 'Name',
                required: true,
                placeholder: 'Child\'s name'
              },
              {
                name: 'date_of_birth',
                type: 'date',
                label: 'Date of Birth',
                required: true
              },
              {
                name: 'gender',
                type: 'select',
                label: 'Gender',
                required: false,
                options: ['male', 'female', 'other']
              },
              {
                name: 'relationship',
                type: 'select',
                label: 'Relationship',
                required: true,
                options: ['child', 'stepchild', 'grandchild', 'sibling', 'other']
              },
              {
                name: 'medical_conditions',
                type: 'tags',
                label: 'Medical Conditions',
                required: false,
                placeholder: 'Add medical conditions (optional)'
              },
              {
                name: 'allergies',
                type: 'tags',
                label: 'Allergies',
                required: false,
                placeholder: 'Add allergies (optional)'
              },
              {
                name: 'is_primary',
                type: 'checkbox',
                label: 'Primary child for care tracking',
                required: false
              }
            ],
            next_step: 'complete'
          }
        }

      default:
        return {
          success: false,
          error: `Unknown onboarding step: ${step}`
        }
    }
  } catch (error) {
    console.error('❌ Error generating onboarding UI:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}